# Admin Interface

FABI+ includes a powerful Django-style admin interface built with HTMX for modern, interactive web administration. This guide covers setting up, customizing, and extending the admin interface.

## Table of Contents

- [Admin Setup](#admin-setup)
- [Model Registration](#model-registration)
- [User Management](#user-management)
- [Custom Admin Views](#custom-admin-views)
- [Admin Permissions](#admin-permissions)
- [Customizing the Interface](#customizing-the-interface)
- [Admin Dashboard](#admin-dashboard)
- [Advanced Features](#advanced-features)

## Admin Setup

### Accessing the Admin Interface

The admin interface is available at `/admin` by default:

```
http://localhost:8000/admin
```

### Creating Admin Users

Create a superuser to access the admin interface:

```bash
# Interactive creation
fabiplus user create --superuser

# With options
fabiplus user create --username admin --email <EMAIL> --superuser
```

### Admin Configuration

Configure admin settings in your project's `settings.py`:

```python
# myproject/settings.py
from fabiplus.conf import FABIPlusSettings

class Settings(FABIPlusSettings):
    # Admin interface settings
    ADMIN_ENABLED: bool = True
    ADMIN_PREFIX: str = "/admin"
    ADMIN_TITLE: str = "My Project Admin"
    ADMIN_SITE_HEADER: str = "My Project Administration"
    ADMIN_INDEX_TITLE: str = "Welcome to My Project Admin"
    
    # Admin authentication
    ADMIN_LOGIN_URL: str = "/admin/login"
    ADMIN_LOGOUT_URL: str = "/admin/logout"
    
    # Admin pagination
    ADMIN_LIST_PER_PAGE: int = 25
    ADMIN_LIST_MAX_SHOW_ALL: int = 200
    
    # Admin media
    ADMIN_MEDIA_PREFIX: str = "/admin/media/"
    
    # Show admin routes in API docs (optional)
    SHOW_ADMIN_ROUTES: bool = False

settings = Settings()
```

### Environment Variables

Configure admin settings via environment variables:

```bash
# .env
ADMIN_ENABLED=true
ADMIN_PREFIX=/admin
ADMIN_TITLE="My Project Admin"
ADMIN_SITE_HEADER="My Project Administration"
ADMIN_LIST_PER_PAGE=25
SHOW_ADMIN_ROUTES=false
```

## Model Registration

### Basic Model Registration

Register models with the admin interface:

```python
# apps/blog/admin.py
from fabiplus.admin import admin
from .models import BlogPost, Category, Tag

# Simple registration
admin.register(BlogPost)
admin.register(Category)
admin.register(Tag)
```

### Custom Admin Classes

Create custom admin classes for advanced configuration:

```python
# apps/blog/admin.py
from fabiplus.admin import admin, ModelAdmin
from .models import BlogPost, Category, Tag

class BlogPostAdmin(ModelAdmin):
    # List view configuration
    list_display = ['title', 'author', 'category', 'status', 'created_at']
    list_filter = ['status', 'category', 'created_at']
    search_fields = ['title', 'content']
    list_per_page = 20
    
    # Form configuration
    fields = ['title', 'slug', 'content', 'excerpt', 'status', 'category', 'tags']
    readonly_fields = ['created_at', 'updated_at', 'view_count']
    
    # Ordering
    ordering = ['-created_at']
    
    # Filters
    date_hierarchy = 'created_at'
    
    # Actions
    actions = ['make_published', 'make_draft']
    
    def make_published(self, request, queryset):
        """Bulk action to publish posts"""
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} posts were published.')
    make_published.short_description = "Mark selected posts as published"
    
    def make_draft(self, request, queryset):
        """Bulk action to make posts draft"""
        updated = queryset.update(status='draft')
        self.message_user(request, f'{updated} posts were marked as draft.')
    make_draft.short_description = "Mark selected posts as draft"

class CategoryAdmin(ModelAdmin):
    list_display = ['name', 'description', 'post_count']
    search_fields = ['name', 'description']
    
    def post_count(self, obj):
        """Display number of posts in category"""
        return obj.posts.count()
    post_count.short_description = 'Posts'

class TagAdmin(ModelAdmin):
    list_display = ['name', 'color', 'post_count']
    list_editable = ['color']
    
    def post_count(self, obj):
        return obj.posts.count()
    post_count.short_description = 'Posts'

# Register with custom admin classes
admin.register(BlogPost, BlogPostAdmin)
admin.register(Category, CategoryAdmin)
admin.register(Tag, TagAdmin)
```

### Admin Field Types

FABI+ automatically generates appropriate form fields based on model field types:

```python
class Product(BaseModel, table=True):
    # Text fields
    name: str = Field(max_length=200)  # → Text input
    description: str  # → Textarea
    
    # Numeric fields
    price: Decimal = Field(decimal_places=2)  # → Number input
    quantity: int  # → Number input
    
    # Boolean fields
    is_active: bool = Field(default=True)  # → Checkbox
    is_featured: bool = Field(default=False)  # → Checkbox
    
    # Date/time fields
    created_at: datetime  # → DateTime picker
    launch_date: Optional[date] = None  # → Date picker
    
    # Choice fields
    status: ProductStatus  # → Select dropdown
    
    # Foreign key fields
    category_id: int = Field(foreign_key="categories.id")  # → Select dropdown
    
    # Many-to-many fields
    tags: List[Tag] = Relationship(link_table="product_tags")  # → Multi-select

class ProductAdmin(ModelAdmin):
    # Customize field widgets
    formfield_overrides = {
        'description': {'widget': 'textarea', 'attrs': {'rows': 4}},
        'price': {'widget': 'number', 'attrs': {'step': '0.01'}},
    }
    
    # Custom field display
    def get_form_field(self, field_name, field_type):
        if field_name == 'status':
            return {
                'widget': 'select',
                'choices': [(s.value, s.value.title()) for s in ProductStatus]
            }
        return super().get_form_field(field_name, field_type)
```

## User Management

### User Admin Configuration

Customize user administration:

```python
# apps/core/admin.py
from fabiplus.admin import admin, ModelAdmin
from fabiplus.core.models import User

class UserAdmin(ModelAdmin):
    list_display = ['username', 'email', 'first_name', 'last_name', 'is_staff', 'is_active', 'date_joined']
    list_filter = ['is_staff', 'is_superuser', 'is_active', 'date_joined']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering = ['username']
    
    # Form configuration
    fieldsets = [
        ('Personal Info', {
            'fields': ['username', 'email', 'first_name', 'last_name']
        }),
        ('Permissions', {
            'fields': ['is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions']
        }),
        ('Important Dates', {
            'fields': ['last_login', 'date_joined'],
            'classes': ['collapse']
        })
    ]
    
    # Read-only fields
    readonly_fields = ['date_joined', 'last_login']
    
    # Filter horizontal for many-to-many fields
    filter_horizontal = ['groups', 'user_permissions']
    
    # Actions
    actions = ['activate_users', 'deactivate_users']
    
    def activate_users(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} users were activated.')
    activate_users.short_description = "Activate selected users"
    
    def deactivate_users(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} users were deactivated.')
    deactivate_users.short_description = "Deactivate selected users"

admin.register(User, UserAdmin)
```

### Group and Permission Management

Manage user groups and permissions:

```python
# apps/core/admin.py
from fabiplus.core.models import Group, Permission

class GroupAdmin(ModelAdmin):
    list_display = ['name', 'permission_count']
    search_fields = ['name']
    filter_horizontal = ['permissions']
    
    def permission_count(self, obj):
        return obj.permissions.count()
    permission_count.short_description = 'Permissions'

class PermissionAdmin(ModelAdmin):
    list_display = ['name', 'content_type', 'codename']
    list_filter = ['content_type']
    search_fields = ['name', 'codename']

admin.register(Group, GroupAdmin)
admin.register(Permission, PermissionAdmin)
```

## Custom Admin Views

### Creating Custom Views

Add custom views to the admin interface:

```python
# apps/blog/admin.py
from fabiplus.admin import admin, AdminView
from fabiplus.core.responses import TemplateResponse
from fastapi import Request, Depends
from .models import BlogPost

class BlogStatsView(AdminView):
    """Custom view for blog statistics"""
    name = "Blog Statistics"
    url_path = "/blog/stats"
    template_name = "admin/blog_stats.html"
    
    async def get(self, request: Request):
        """Display blog statistics"""
        # Get statistics
        total_posts = await BlogPost.count()
        published_posts = await BlogPost.filter(status="published").count()
        draft_posts = await BlogPost.filter(status="draft").count()
        
        # Recent posts
        recent_posts = await BlogPost.filter().order_by("-created_at").limit(10)
        
        context = {
            "title": "Blog Statistics",
            "total_posts": total_posts,
            "published_posts": published_posts,
            "draft_posts": draft_posts,
            "recent_posts": recent_posts,
        }
        
        return TemplateResponse(request, self.template_name, context)

class BulkImportView(AdminView):
    """Custom view for bulk importing posts"""
    name = "Bulk Import"
    url_path = "/blog/import"
    template_name = "admin/bulk_import.html"
    
    async def get(self, request: Request):
        """Display import form"""
        return TemplateResponse(request, self.template_name, {
            "title": "Bulk Import Posts"
        })
    
    async def post(self, request: Request):
        """Handle import submission"""
        form = await request.form()
        csv_file = form.get("csv_file")
        
        if not csv_file:
            return TemplateResponse(request, self.template_name, {
                "title": "Bulk Import Posts",
                "error": "Please select a CSV file"
            })
        
        # Process CSV file
        imported_count = await self.process_csv_import(csv_file)
        
        return TemplateResponse(request, self.template_name, {
            "title": "Bulk Import Posts",
            "success": f"Successfully imported {imported_count} posts"
        })
    
    async def process_csv_import(self, csv_file):
        """Process CSV file and import posts"""
        import csv
        import io
        
        content = await csv_file.read()
        csv_data = csv.DictReader(io.StringIO(content.decode('utf-8')))
        
        imported_count = 0
        for row in csv_data:
            post = BlogPost(
                title=row['title'],
                content=row['content'],
                status=row.get('status', 'draft')
            )
            await post.save()
            imported_count += 1
        
        return imported_count

# Register custom views
admin.register_view(BlogStatsView)
admin.register_view(BulkImportView)
```

### Custom Templates

Create custom templates for admin views:

```html
<!-- templates/admin/blog_stats.html -->
{% extends "admin/base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="admin-content">
    <h1>{{ title }}</h1>
    
    <div class="stats-grid">
        <div class="stat-card">
            <h3>Total Posts</h3>
            <p class="stat-number">{{ total_posts }}</p>
        </div>
        
        <div class="stat-card">
            <h3>Published</h3>
            <p class="stat-number">{{ published_posts }}</p>
        </div>
        
        <div class="stat-card">
            <h3>Drafts</h3>
            <p class="stat-number">{{ draft_posts }}</p>
        </div>
    </div>
    
    <div class="recent-posts">
        <h2>Recent Posts</h2>
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Status</th>
                    <th>Created</th>
                </tr>
            </thead>
            <tbody>
                {% for post in recent_posts %}
                <tr>
                    <td>{{ post.title }}</td>
                    <td>{{ post.status }}</td>
                    <td>{{ post.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
```

## Admin Permissions

### Permission-Based Access

Control access to admin sections based on user permissions:

```python
# apps/blog/admin.py
from fabiplus.admin import admin, ModelAdmin
from fabiplus.core.permissions import has_permission

class BlogPostAdmin(ModelAdmin):
    def has_view_permission(self, request, obj=None):
        """Check if user can view blog posts"""
        return has_permission(request.user, 'blog.view_blogpost')

    def has_add_permission(self, request):
        """Check if user can add blog posts"""
        return has_permission(request.user, 'blog.add_blogpost')

    def has_change_permission(self, request, obj=None):
        """Check if user can change blog posts"""
        if obj and obj.author != request.user and not request.user.is_staff:
            return False
        return has_permission(request.user, 'blog.change_blogpost')

    def has_delete_permission(self, request, obj=None):
        """Check if user can delete blog posts"""
        if obj and obj.author != request.user and not request.user.is_superuser:
            return False
        return has_permission(request.user, 'blog.delete_blogpost')

    def get_queryset(self, request):
        """Filter queryset based on user permissions"""
        qs = super().get_queryset(request)

        # Non-staff users can only see their own posts
        if not request.user.is_staff:
            qs = qs.filter(author=request.user)

        return qs

admin.register(BlogPost, BlogPostAdmin)
```

### Role-Based Admin Access

Create role-based admin access:

```python
# apps/core/admin.py
from fabiplus.admin import admin, AdminSite

class StaffAdminSite(AdminSite):
    """Admin site for staff users"""
    site_header = "Staff Administration"
    site_title = "Staff Admin"
    index_title = "Staff Dashboard"

    def has_permission(self, request):
        """Only allow staff users"""
        return request.user.is_authenticated and request.user.is_staff

class SuperuserAdminSite(AdminSite):
    """Admin site for superusers only"""
    site_header = "Superuser Administration"
    site_title = "Superuser Admin"
    index_title = "Superuser Dashboard"

    def has_permission(self, request):
        """Only allow superusers"""
        return request.user.is_authenticated and request.user.is_superuser

# Create custom admin sites
staff_admin = StaffAdminSite(name='staff_admin')
superuser_admin = SuperuserAdminSite(name='superuser_admin')

# Register models with different admin sites
staff_admin.register(BlogPost, BlogPostAdmin)
superuser_admin.register(User, UserAdmin)
```

## Customizing the Interface

### Custom Admin Templates

Override default admin templates:

```html
<!-- templates/admin/base.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ admin_site.site_title }}{% endblock %}</title>

    <!-- Admin CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='admin/css/admin.css') }}">

    <!-- Custom CSS -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="admin-wrapper">
        <!-- Header -->
        <header class="admin-header">
            <div class="admin-branding">
                <h1>{{ admin_site.site_header }}</h1>
            </div>

            <div class="admin-user-tools">
                {% if user.is_authenticated %}
                    <span>Welcome, {{ user.username }}</span>
                    <a href="{{ admin_site.logout_url }}">Logout</a>
                {% endif %}
            </div>
        </header>

        <!-- Navigation -->
        <nav class="admin-nav">
            <ul>
                <li><a href="{{ admin_site.index_url }}">Dashboard</a></li>
                {% for app in admin_site.get_app_list() %}
                    <li class="app-{{ app.app_label }}">
                        <span class="app-name">{{ app.name }}</span>
                        <ul>
                            {% for model in app.models %}
                                <li><a href="{{ model.admin_url }}">{{ model.name }}</a></li>
                            {% endfor %}
                        </ul>
                    </li>
                {% endfor %}
            </ul>
        </nav>

        <!-- Main content -->
        <main class="admin-main">
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Admin JavaScript -->
    <script src="{{ url_for('static', path='admin/js/admin.js') }}"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Custom JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
```

### Custom CSS Styling

Add custom styling to the admin interface:

```css
/* static/admin/css/custom.css */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

.admin-wrapper {
    display: grid;
    grid-template-areas:
        "header header"
        "nav main";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 60px 1fr;
    min-height: 100vh;
}

.admin-header {
    grid-area: header;
    background: var(--primary-color);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.admin-nav {
    grid-area: nav;
    background: var(--light-color);
    border-right: 1px solid #dee2e6;
    padding: 20px 0;
}

.admin-main {
    grid-area: main;
    padding: 20px;
    background: white;
}

/* Form styling */
.admin-form {
    max-width: 800px;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button styling */
.btn {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary {
    color: white;
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    color: white;
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    color: white;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Table styling */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.admin-table th,
.admin-table td {
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    text-align: left;
}

.admin-table th {
    background-color: var(--light-color);
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.admin-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Responsive design */
@media (max-width: 768px) {
    .admin-wrapper {
        grid-template-areas:
            "header"
            "nav"
            "main";
        grid-template-columns: 1fr;
        grid-template-rows: 60px auto 1fr;
    }

    .admin-nav {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
    }
}
```
