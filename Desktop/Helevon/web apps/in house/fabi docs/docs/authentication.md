# FABI+ Authentication Guide

This guide covers authentication implementation in FABI+ framework, including built-in authentication and custom authentication backends.

## Built-in Authentication

FABI+ comes with a built-in User model and OAuth2/JWT authentication system.

### User Model

The framework provides a default User model with the following fields:

```python
class User(SQLModel, table=True):
    __tablename__ = "users"
    
    id: uuid.UUID = Field(primary_key=True)
    username: str = Field(unique=True, max_length=150)
    email: str = Field(unique=True, max_length=254)
    first_name: Optional[str] = Field(max_length=150)
    last_name: Optional[str] = Field(max_length=150)
    is_active: bool = Field(default=True)
    is_staff: bool = Field(default=False)
    is_superuser: bool = Field(default=False)
    hashed_password: str = Field(max_length=128)
    last_login: Optional[datetime] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
```

### Authentication Endpoints

The framework automatically provides these authentication endpoints:

- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/register` - User registration (if enabled)
- `GET /auth/me` - Get current user info
- `POST /auth/refresh` - Refresh JWT token

### Environment Configuration

Configure authentication in your `.env` file:

```env
# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Authentication
ENABLE_REGISTRATION=true
REQUIRE_EMAIL_VERIFICATION=false
```

## Custom Authentication Backend

You can implement custom authentication by creating a custom authentication backend.

### Step 1: Create Custom User Model

If you need additional fields, extend the base User model:

```python
# apps/auth/models.py
from fabiplus.core.user_model import User as BaseUser
from fabiplus.core.models import register_model
from sqlmodel import Field
from typing import Optional

@register_model
class CustomUser(BaseUser, table=True):
    __tablename__ = "custom_users"
    
    # Additional fields
    phone_number: Optional[str] = Field(max_length=20)
    department: Optional[str] = Field(max_length=100)
    employee_id: Optional[str] = Field(max_length=50)
    
    class Config:
        _verbose_name = "Custom User"
        _verbose_name_plural = "Custom Users"
```

### Step 2: Create Authentication Backend

```python
# apps/auth/backends.py
from typing import Optional
from fabiplus.core.auth.base import BaseAuthBackend
from fabiplus.core.auth.models import User
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class CustomAuthBackend(BaseAuthBackend):
    """Custom authentication backend"""
    
    async def authenticate(self, username: str, password: str) -> Optional[User]:
        """Authenticate user with username/password"""
        # Custom authentication logic here
        user = await self.get_user_by_username(username)
        if user and self.verify_password(password, user.hashed_password):
            return user
        return None
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        from fabiplus.core.models import ModelRegistry
        session = ModelRegistry.get_session()
        # Implementation depends on your ORM
        # For SQLModel:
        from sqlmodel import select
        statement = select(User).where(User.username == username)
        result = session.exec(statement)
        return result.first()
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def hash_password(self, password: str) -> str:
        """Hash password"""
        return pwd_context.hash(password)
```

### Step 3: Configure Custom Backend

Update your settings to use the custom backend:

```python
# settings.py
AUTHENTICATION_BACKEND = "apps.auth.backends.CustomAuthBackend"
```

## OAuth2 Integration

FABI+ supports OAuth2 integration with popular providers.

### Google OAuth2

```python
# settings.py
OAUTH2_PROVIDERS = {
    "google": {
        "client_id": "your-google-client-id",
        "client_secret": "your-google-client-secret",
        "redirect_uri": "http://localhost:8000/auth/oauth2/google/callback",
        "scopes": ["openid", "email", "profile"],
    }
}
```

### Custom OAuth2 Provider

```python
# apps/auth/oauth.py
from fabiplus.core.auth.oauth import BaseOAuth2Provider

class CustomOAuth2Provider(BaseOAuth2Provider):
    """Custom OAuth2 provider"""
    
    def get_authorization_url(self) -> str:
        """Get authorization URL"""
        # Implementation here
        pass
    
    async def get_user_info(self, access_token: str) -> dict:
        """Get user info from provider"""
        # Implementation here
        pass
```

## Permission System

FABI+ includes a comprehensive permission system.

### Model-Level Permissions

```python
# apps/blog/models.py
from fabiplus.core.models import BaseModel
from fabiplus.core.permissions import ModelPermission

@register_model
class Post(BaseModel, table=True):
    title: str = Field(max_length=200)
    content: str = Field()
    author_id: uuid.UUID = Field(foreign_key="users.id")
    
    class Meta:
        permissions = [
            ("view_post", "Can view post"),
            ("add_post", "Can add post"),
            ("change_post", "Can change post"),
            ("delete_post", "Can delete post"),
            ("publish_post", "Can publish post"),  # Custom permission
        ]
```

### View-Level Permissions

```python
# apps/blog/views.py
from fabiplus.core.permissions import require_permission
from fabiplus.api.views import APIView

class PostView(APIView):
    @require_permission("blog.view_post")
    async def get(self, request):
        """Get posts - requires view permission"""
        pass
    
    @require_permission("blog.add_post")
    async def post(self, request):
        """Create post - requires add permission"""
        pass
```

## Testing Authentication

### Unit Tests

```python
# tests/test_auth.py
import pytest
from fabiplus.core.auth.backends import CustomAuthBackend

@pytest.mark.asyncio
async def test_custom_authentication():
    backend = CustomAuthBackend()
    
    # Test successful authentication
    user = await backend.authenticate("testuser", "testpass")
    assert user is not None
    assert user.username == "testuser"
    
    # Test failed authentication
    user = await backend.authenticate("testuser", "wrongpass")
    assert user is None
```

### Integration Tests

```python
# tests/test_auth_api.py
import pytest
from fastapi.testclient import TestClient

def test_login_endpoint(client: TestClient):
    response = client.post("/auth/login", json={
        "username": "testuser",
        "password": "testpass"
    })
    assert response.status_code == 200
    assert "access_token" in response.json()
```

## Best Practices

1. **Always hash passwords** - Never store plain text passwords
2. **Use strong secrets** - Generate secure SECRET_KEY values
3. **Implement rate limiting** - Prevent brute force attacks
4. **Validate input** - Always validate authentication input
5. **Log authentication events** - Track login/logout for security
6. **Use HTTPS in production** - Protect authentication tokens
7. **Implement proper session management** - Handle token expiration
8. **Follow principle of least privilege** - Grant minimal required permissions

## Troubleshooting

### Common Issues

1. **Foreign Key Errors**: When creating models with user relationships, use the correct table name:
   ```python
   # Correct - use table name "users"
   author_id: uuid.UUID = Field(foreign_key="users.id")
   
   # Incorrect - don't use model name "user"
   author_id: uuid.UUID = Field(foreign_key="user.id")
   ```

2. **Environment Variable Parsing**: Boolean values in .env files should be lowercase:
   ```env
   # Correct
   DEBUG=true
   ADMIN_ROUTES_IN_DOCS=false
   
   # Incorrect
   DEBUG=True
   ADMIN_ROUTES_IN_DOCS=False
   ```

3. **Database URL Configuration**: Ensure project-specific database names:
   ```env
   # Project-specific database
   DATABASE_URL=sqlite:///./myproject.db
   ```

## Complete Example: Custom Authentication

Here's a complete example of implementing custom authentication in a FABI+ project:

### 1. Create the Project

```bash
fabiplus project startproject myauth --orm sqlmodel
cd myauth
```

### 2. Create Auth App

```bash
fabiplus app startapp auth
```

### 3. Implement Custom User Model

```python
# apps/auth/models.py
import uuid
from datetime import datetime
from typing import Optional
from sqlmodel import Field
from fabiplus.core.models import BaseModel, register_model

@register_model
class CustomUser(BaseModel, table=True):
    __tablename__ = "custom_users"

    # Basic fields
    username: str = Field(unique=True, max_length=150)
    email: str = Field(unique=True, max_length=254)
    first_name: Optional[str] = Field(max_length=150)
    last_name: Optional[str] = Field(max_length=150)
    hashed_password: str = Field(max_length=128)

    # Custom fields
    employee_id: Optional[str] = Field(max_length=50)
    department: Optional[str] = Field(max_length=100)
    phone: Optional[str] = Field(max_length=20)

    # Status fields
    is_active: bool = Field(default=True)
    is_staff: bool = Field(default=False)
    is_superuser: bool = Field(default=False)
    last_login: Optional[datetime] = Field(default=None)

    class Config:
        _verbose_name = "Custom User"
        _verbose_name_plural = "Custom Users"

    def __str__(self):
        return self.username
```

### 4. Create Authentication Views

```python
# apps/auth/views.py
from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel
from fabiplus.core.auth import create_access_token, get_current_user
from .models import CustomUser
from .backends import CustomAuthBackend

router = APIRouter(prefix="/auth", tags=["authentication"])
auth_backend = CustomAuthBackend()

class UserResponse(BaseModel):
    id: str
    username: str
    email: str
    first_name: str
    last_name: str
    department: str
    is_staff: bool

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    user = await auth_backend.authenticate(form_data.username, form_data.password)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid credentials")

    access_token = create_access_token(data={"sub": user.username})
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: CustomUser = Depends(get_current_user)):
    return current_user
```

### 5. Run Migrations

```bash
fabiplus db makemigrations
fabiplus db migrate
```

### 6. Create Test User

```bash
fabiplus user <NAME_EMAIL> --superuser
```

### 7. Test the API

```bash
fabiplus server run
```

Then test with curl:

```bash
# Login
curl -X POST "http://localhost:8000/auth/login" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=admin&password=your_password"

# Get user info (replace TOKEN with the access_token from login)
curl -X GET "http://localhost:8000/auth/me" \
     -H "Authorization: Bearer TOKEN"
```

## Authentication vs Authorization Patterns

### Model-Level Authentication

Model-level authentication applies security rules directly to your data models. This approach is useful when you want consistent security across all access points to your data.

```python
# apps/blog/models.py
from fabiplus.core.models import BaseModel
from fabiplus.core.permissions import require_permission
from sqlmodel import Field

class Post(BaseModel, table=True):
    title: str = Field(max_length=200)
    content: str
    author_id: str = Field(foreign_key="user.id")
    is_published: bool = Field(default=False)

    class Meta:
        # Model-level permissions
        permissions = {
            'create': ['blog.add_post', 'is_staff'],
            'read': ['blog.view_post'],
            'update': ['blog.change_post', 'is_author_or_staff'],
            'delete': ['blog.delete_post', 'is_superuser']
        }

    def can_edit(self, user):
        """Custom permission check"""
        return user.is_staff or user.id == self.author_id
```

### View-Level Authentication

View-level authentication applies security at the API endpoint level. This gives you fine-grained control over specific operations.

```python
# apps/blog/views.py
from fastapi import APIRouter, Depends, HTTPException
from fabiplus.core.auth import get_current_active_user, get_current_staff_user
from fabiplus.core.permissions import has_permission
from .models import Post

router = APIRouter()

@router.get("/posts/")
async def list_posts(
    current_user = Depends(get_current_active_user)
):
    """List posts - requires authentication"""
    # Only show published posts to regular users
    if current_user.is_staff:
        return Post.get_all()
    else:
        return Post.filter(is_published=True)

@router.post("/posts/")
async def create_post(
    post_data: PostCreate,
    current_user = Depends(get_current_staff_user)  # Staff required
):
    """Create post - requires staff access"""
    post = Post(**post_data.dict(), author_id=current_user.id)
    return post.save()

@router.put("/posts/{post_id}")
async def update_post(
    post_id: str,
    post_data: PostUpdate,
    current_user = Depends(has_permission("blog.change_post"))
):
    """Update post - requires specific permission"""
    post = Post.get_by_id(post_id)
    if not post.can_edit(current_user):
        raise HTTPException(status_code=403, detail="Permission denied")

    return post.update(**post_data.dict())
```

### Permission System Integration

FABI+ includes a comprehensive permission system that integrates with authentication:

```python
# Custom permission decorators
from fabiplus.core.permissions import permission_required, role_required

@router.get("/admin/posts/")
@permission_required("blog.view_all_posts")
async def admin_list_posts(current_user = Depends(get_current_active_user)):
    """Admin view - requires specific permission"""
    return Post.get_all()

@router.delete("/posts/{post_id}")
@role_required("editor")
async def delete_post(
    post_id: str,
    current_user = Depends(get_current_active_user)
):
    """Delete post - requires editor role"""
    post = Post.get_by_id(post_id)
    return post.delete()
```

### Protecting CRUD Operations

Here's how to protect different CRUD operations with various authentication patterns:

```python
# apps/blog/protected_views.py
from fastapi import APIRouter, Depends, HTTPException, status
from fabiplus.core.auth import (
    get_current_active_user,
    get_current_staff_user,
    get_current_superuser
)
from fabiplus.core.permissions import has_permission, check_object_permission

router = APIRouter(prefix="/protected", tags=["Protected Blog"])

# CREATE - Staff only
@router.post("/posts/", response_model=PostResponse)
async def create_protected_post(
    post_data: PostCreate,
    current_user = Depends(get_current_staff_user)
):
    """Create post - staff access required"""
    post = Post(
        **post_data.dict(),
        author_id=current_user.id
    )
    return post.save()

# READ - Public for published, staff for all
@router.get("/posts/{post_id}", response_model=PostResponse)
async def get_protected_post(
    post_id: str,
    current_user = Depends(get_current_active_user)
):
    """Get post - authentication required"""
    post = Post.get_by_id(post_id)

    # Check if user can view this post
    if not post.is_published and not current_user.is_staff:
        if current_user.id != post.author_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Cannot view unpublished post"
            )

    return post

# UPDATE - Author or staff only
@router.put("/posts/{post_id}", response_model=PostResponse)
async def update_protected_post(
    post_id: str,
    post_data: PostUpdate,
    current_user = Depends(get_current_active_user)
):
    """Update post - author or staff required"""
    post = Post.get_by_id(post_id)

    # Check permissions
    if not (current_user.is_staff or current_user.id == post.author_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )

    return post.update(**post_data.dict())

# DELETE - Superuser only
@router.delete("/posts/{post_id}")
async def delete_protected_post(
    post_id: str,
    current_user = Depends(get_current_superuser)
):
    """Delete post - superuser required"""
    post = Post.get_by_id(post_id)
    post.delete()
    return {"message": "Post deleted successfully"}
```

### Advanced Authentication Patterns

#### API Key Authentication

```python
from fabiplus.core.custom_auth import APIKeyAuth

api_key_auth = APIKeyAuth()

@router.get("/api-data/")
async def get_api_data(
    current_user = Depends(api_key_auth)
):
    """Access via API key"""
    return {"data": "sensitive information"}
```

#### Session-Based Authentication

```python
from fabiplus.core.custom_auth import SessionAuth

session_auth = SessionAuth()

@router.get("/session-data/")
async def get_session_data(
    current_user = Depends(session_auth)
):
    """Access via session"""
    return {"user": current_user.username}
```

## Troubleshooting

### Common Issues

1. **Authentication fails with 401 error**
   - Check if user exists and is active
   - Verify password is correct
   - Ensure JWT secret key is set properly

2. **Token expires too quickly**
   - Adjust `ACCESS_TOKEN_EXPIRE_MINUTES` in settings
   - Consider implementing refresh tokens

3. **Custom backend not loading**
   - Check the import path in `AUTH_BACKEND` setting
   - Ensure the backend class inherits from `BaseAuthBackend`
   - Verify all required methods are implemented

4. **Permission denied errors**
   - Check user roles and permissions
   - Verify the permission checking logic
   - Ensure proper inheritance from base classes

5. **Swagger UI authentication not working**
   - Verify OAuth2 token URL is correct (`/auth/token`)
   - Check that the token endpoint accepts form data
   - Ensure CORS is configured properly for browser requests

6. **Admin interface login issues**
   - Check admin static files are served correctly
   - Verify admin routes are properly configured
   - Ensure session/cookie authentication is working

7. **Static files not loading in admin**
   - Verify static file paths in templates use `/admin/static/` prefix
   - Check that admin static files are mounted correctly
   - Ensure proper MIME types are being served

For more help, check the [FABI+ documentation](https://fabiplus.helevon.org) or open an issue on GitHub.
