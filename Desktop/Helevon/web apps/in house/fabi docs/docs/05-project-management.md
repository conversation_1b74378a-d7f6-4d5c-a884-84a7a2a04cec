# Project Management

This guide covers creating, configuring, and managing FABI+ projects, including project structure, app management, database migrations, and the media system.

## Project Creation

### Creating a New Project

Use the `startproject` command to create a new FABI+ project:

```bash
fabiplus project startproject myproject
```

This creates a complete project structure with:
- Project configuration files
- Poetry setup with dependencies
- Environment configuration
- Basic app structure
- Example models and views

### Project Creation Options

**ORM Backend Selection:**
```bash
# SQLModel (default) - Modern, type-safe ORM
fabiplus project startproject myapi --orm sqlmodel

# SQLAlchemy - Mature, feature-rich ORM
fabiplus project startproject myapi --orm sqlalchemy
```

**Authentication Backend:**
```bash
# OAuth2 (default) - Industry standard
fabiplus project startproject myapi --auth oauth2

# JWT - JSON Web Tokens
fabiplus project startproject myapi --auth jwt
```

**Docker Support:**
```bash
# Include Docker files
fabiplus project startproject myapi --docker
```

**Admin Routes Visibility:**
```bash
# Show admin routes in API documentation
fabiplus project startproject myapi --show-admin-routes
```

**Complete Example:**
```bash
fabiplus project startproject ecommerce \
  --orm sqlalchemy \
  --auth jwt \
  --docker \
  --show-admin-routes
```

## Project Structure

A FABI+ project follows this structure:

```
myproject/
├── myproject/                 # Main project package
│   ├── __init__.py
│   ├── settings.py           # Project configuration
│   ├── urls.py              # URL routing
│   └── wsgi.py              # WSGI application
├── apps/                    # Project applications
│   └── core/                # Default core app
│       ├── __init__.py
│       ├── models.py        # Database models
│       ├── views.py         # API endpoints
│       ├── admin.py         # Admin configuration
│       ├── serializers.py   # Pydantic schemas
│       ├── urls.py          # App URL routing
│       └── tests.py         # Test cases
├── static/                  # Static files (CSS, JS, images)
├── media/                   # User-uploaded files
├── logs/                    # Application logs
├── migrations/              # Database migration files
├── manage.py               # Management script
├── pyproject.toml          # Poetry configuration
├── .env                    # Environment variables
├── .env.example            # Environment template
├── .gitignore             # Git ignore rules
├── README.md              # Project documentation
└── Dockerfile             # Docker configuration (optional)
```

### Key Files Explained

**`myproject/settings.py`** - Main configuration file:
```python
from fabiplus.conf import FABIPlusSettings

class Settings(FABIPlusSettings):
    # Project-specific settings
    PROJECT_NAME: str = "My Project"
    VERSION: str = "1.0.0"
    
    # Database configuration
    DATABASE_URL: str = "sqlite:///./myproject.db"
    
    # Installed apps
    INSTALLED_APPS: List[str] = [
        "apps.core",
        "apps.blog",  # Add your apps here
    ]

settings = Settings()
```

**`myproject/urls.py`** - URL routing configuration:
```python
from fabiplus.api import APIRouter
from apps.core import urls as core_urls
from apps.blog import urls as blog_urls

# Main API router
api_router = APIRouter()

# Include app routers
api_router.include_router(core_urls.router, prefix="/core", tags=["Core"])
api_router.include_router(blog_urls.router, prefix="/blog", tags=["Blog"])
```

**`manage.py`** - Management script:
```python
#!/usr/bin/env python
import os
import sys

if __name__ == "__main__":
    os.environ.setdefault("FABIPLUS_SETTINGS_MODULE", "myproject.settings")
    
    from fabiplus.cli import main
    main()
```

## App Management

### Creating Apps

Apps are modular components that organize related functionality:

```bash
# Create a basic app
fabiplus app startapp blog

# Create with specific template
fabiplus app startapp shop --template ecommerce

# Create in custom directory
fabiplus app startapp api --dir custom_apps
```

### App Templates

FABI+ provides several app templates:

**Default Template** - Standard app with all components:
```bash
fabiplus app startapp blog --template default
```

**API Template** - Focused on API development:
```bash
fabiplus app startapp api --template api
```

**CRUD Template** - Full CRUD operations:
```bash
fabiplus app startapp products --template crud
```

**Auth Template** - User authentication:
```bash
fabiplus app startapp accounts --template auth
```

### App Structure

Each app contains these files:

```
apps/blog/
├── __init__.py
├── models.py          # Database models
├── views.py           # API endpoints
├── admin.py           # Admin interface
├── serializers.py     # Pydantic schemas
├── urls.py            # URL routing
└── tests.py           # Test cases
```

### Registering Apps

Add your app to `INSTALLED_APPS` in `settings.py`:

```python
INSTALLED_APPS: List[str] = [
    "apps.core",
    "apps.blog",      # Add your new app
    "apps.shop",      # Another app
]
```

### App Generation

Generate complete app components automatically:

```bash
# Generate a blog post model with fields
fabiplus app generate blog Post --fields "title:str,content:text,published:bool"
```

This creates:
- Model class in `models.py`
- API views in `views.py`
- Admin configuration in `admin.py`
- Pydantic schemas in `serializers.py`
- URL routing in `urls.py`
- Test cases in `tests.py`

## Database Migrations

FABI+ uses Alembic for database migrations, similar to Django's migration system.

### Creating Migrations

**Auto-generate migrations:**
```bash
fabiplus db makemigrations
```

**With custom message:**
```bash
fabiplus db makemigrations -m "Add user profile model"
```

**For specific app:**
```bash
fabiplus db makemigrations apps.blog -m "Add blog post model"
```

### Applying Migrations

**Apply all pending migrations:**
```bash
fabiplus db migrate
```

**Migrate to specific revision:**
```bash
fabiplus db migrate abc123
```

**Show migration status:**
```bash
fabiplus db showmigrations
```

### Migration Files

Migrations are stored in the `migrations/` directory:

```
migrations/
├── env.py                    # Alembic environment
├── script.py.mako           # Migration template
├── alembic.ini              # Alembic configuration
└── versions/                # Migration versions
    ├── 001_initial_migration.py
    ├── 002_add_user_profile.py
    └── 003_add_blog_posts.py
```

### Example Migration

```python
"""Add blog post model

Revision ID: abc123
Revises: def456
Create Date: 2024-01-15 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = 'abc123'
down_revision = 'def456'
branch_labels = None
depends_on = None

def upgrade():
    # Create blog_posts table
    op.create_table('blog_posts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('published', sa.Boolean(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    # Drop blog_posts table
    op.drop_table('blog_posts')
```

### Database Management

**Reset database (WARNING: Deletes all data):**
```bash
fabiplus db reset --force
```

**Create tables from models:**
```bash
fabiplus db create-tables
```

**Check database information:**
```bash
fabiplus db info
```

## Media System

FABI+ includes a comprehensive media system for handling file uploads, processing, and serving.

### Media Configuration

Configure media settings in your `.env` file:

```bash
# File upload settings
MAX_UPLOAD_SIZE=10485760          # 10MB in bytes
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx
MEDIA_ROOT=media/                 # Media files directory
MEDIA_URL=/media/                 # Media URL prefix

# Image processing
IMAGE_THUMBNAILS=true             # Generate thumbnails
THUMBNAIL_SIZES=150x150,300x300   # Thumbnail dimensions
IMAGE_QUALITY=85                  # JPEG quality (1-100)

# Storage backend
MEDIA_STORAGE=local               # local, s3, gcs
```

### File Upload Model

Create models with file fields:

```python
from fabiplus.core.models import BaseModel
from fabiplus.core.media.fields import ImageField, FileField
from sqlmodel import Field

class BlogPost(BaseModel, table=True):
    __tablename__ = "blog_posts"
    
    title: str = Field(max_length=200)
    content: str
    featured_image: Optional[str] = ImageField(
        upload_to="blog/images/",
        max_size=5242880,  # 5MB
        allowed_extensions=["jpg", "jpeg", "png"]
    )
    attachment: Optional[str] = FileField(
        upload_to="blog/files/",
        max_size=10485760,  # 10MB
        allowed_extensions=["pdf", "doc", "docx"]
    )
```

### File Upload API

Create file upload endpoints:

```python
from fabiplus.api import APIRouter
from fabiplus.core.media import MediaHandler
from fastapi import UploadFile, File

router = APIRouter()

@router.post("/upload-image")
async def upload_image(file: UploadFile = File(...)):
    """Upload an image file"""
    media_handler = MediaHandler()
    
    # Validate and save file
    file_path = await media_handler.save_file(
        file=file,
        upload_to="uploads/images/",
        allowed_extensions=["jpg", "jpeg", "png"],
        max_size=5242880  # 5MB
    )
    
    return {"file_path": file_path, "url": f"/media/{file_path}"}

@router.post("/upload-document")
async def upload_document(file: UploadFile = File(...)):
    """Upload a document file"""
    media_handler = MediaHandler()
    
    file_path = await media_handler.save_file(
        file=file,
        upload_to="uploads/documents/",
        allowed_extensions=["pdf", "doc", "docx"],
        max_size=10485760  # 10MB
    )
    
    return {"file_path": file_path, "url": f"/media/{file_path}"}
```

### Image Processing

FABI+ automatically processes images:

```python
from fabiplus.core.media.processors import ImageProcessor

# Create thumbnails
processor = ImageProcessor()
thumbnail_path = processor.create_thumbnail(
    image_path="media/uploads/image.jpg",
    size=(300, 300),
    quality=85
)

# Resize image
resized_path = processor.resize_image(
    image_path="media/uploads/image.jpg",
    width=800,
    height=600,
    maintain_aspect_ratio=True
)

# Get image metadata
metadata = processor.get_metadata("media/uploads/image.jpg")
print(f"Dimensions: {metadata['width']}x{metadata['height']}")
print(f"Format: {metadata['format']}")
print(f"Size: {metadata['size']} bytes")
```

### File Serving

FABI+ automatically serves media files:

```python
# In your main application
from fabiplus.core.media import setup_media_serving

app = FastAPI()

# Setup media file serving
setup_media_serving(app, media_root="media/", media_url="/media/")
```

Files are accessible at:
- `http://localhost:8000/media/uploads/image.jpg`
- `http://localhost:8000/media/blog/images/featured.png`

### Storage Backends

**Local Storage (Default):**
```python
MEDIA_STORAGE = "local"
MEDIA_ROOT = "media/"
```

**Amazon S3:**
```python
MEDIA_STORAGE = "s3"
AWS_ACCESS_KEY_ID = "your-access-key"
AWS_SECRET_ACCESS_KEY = "your-secret-key"
AWS_STORAGE_BUCKET_NAME = "your-bucket"
AWS_S3_REGION_NAME = "us-east-1"
```

**Google Cloud Storage:**
```python
MEDIA_STORAGE = "gcs"
GCS_BUCKET_NAME = "your-bucket"
GCS_CREDENTIALS_FILE = "path/to/credentials.json"
```

## Environment Configuration

### Environment Files

FABI+ supports multiple environment configurations:

**Development (`.env.development`):**
```bash
DEBUG=true
ENVIRONMENT=development
DATABASE_URL=sqlite:///./dev.db
LOG_LEVEL=DEBUG
CACHE_BACKEND=memory
```

**Testing (`.env.testing`):**
```bash
DEBUG=false
ENVIRONMENT=testing
DATABASE_URL=sqlite:///./test.db
LOG_LEVEL=WARNING
CACHE_BACKEND=memory
```

**Production (`.env.production`):**
```bash
DEBUG=false
ENVIRONMENT=production
DATABASE_URL=***********************************/myproject
LOG_LEVEL=INFO
CACHE_BACKEND=redis
REDIS_URL=redis://prod-redis:6379/0
```

### Loading Environment

Set the environment when running commands:

```bash
# Development (default)
fabiplus server run

# Testing
FABIPLUS_ENV=testing fabiplus server run

# Production
FABIPLUS_ENV=production fabiplus server run
```

## Project Deployment

### Docker Deployment

If you created your project with `--docker`, you'll have these files:

**`Dockerfile`:**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install Poetry
RUN pip install poetry

# Copy dependency files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev

# Copy application
COPY . .

# Expose port
EXPOSE 8000

# Run application
CMD ["fabiplus", "server", "run", "--host", "0.0.0.0", "--port", "8000"]
```

**`docker-compose.yml`:**
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/myproject
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=myproject
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine

volumes:
  postgres_data:
```

**Build and run:**
```bash
# Build and start services
docker-compose up --build

# Run in background
docker-compose up -d

# View logs
docker-compose logs -f web
```

### Production Checklist

Before deploying to production:

1. **Security Settings:**
   - [ ] Change `SECRET_KEY` and `JWT_SECRET_KEY`
   - [ ] Set `DEBUG=false`
   - [ ] Configure proper CORS origins
   - [ ] Enable HTTPS

2. **Database:**
   - [ ] Use PostgreSQL or MySQL
   - [ ] Configure connection pooling
   - [ ] Set up database backups

3. **Caching:**
   - [ ] Use Redis for caching
   - [ ] Configure cache TTL settings

4. **Logging:**
   - [ ] Set appropriate log levels
   - [ ] Configure log rotation
   - [ ] Set up log monitoring

5. **Performance:**
   - [ ] Use multiple workers
   - [ ] Configure static file serving
   - [ ] Set up CDN for media files

6. **Monitoring:**
   - [ ] Set up health checks
   - [ ] Configure error tracking
   - [ ] Monitor performance metrics

---

**Previous**: [← CLI Commands Reference](04-cli-commands.md) | **Next**: [API Development →](06-api-development.md)
