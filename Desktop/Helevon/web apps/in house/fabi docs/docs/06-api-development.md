# API Development

This guide covers building APIs with FABI+, including models, CRUD operations, custom endpoints, authentication, and permissions.

## Table of Contents

- [Models and Database](#models-and-database)
- [Pydantic Schemas](#pydantic-schemas)
- [API Views and Endpoints](#api-views-and-endpoints)
- [CRUD Operations](#crud-operations)
- [Custom Endpoints](#custom-endpoints)
- [Authentication](#authentication)
- [Permissions and Authorization](#permissions-and-authorization)
- [Request/Response Handling](#requestresponse-handling)
- [Error Handling](#error-handling)
- [API Documentation](#api-documentation)

## Models and Database

### Creating Models

FABI+ uses SQLModel for type-safe database models:

```python
# apps/blog/models.py
from fabiplus.core.models import BaseModel
from sqlmodel import Field, Relationship
from typing import Optional, List
from datetime import datetime
from enum import Enum

class PostStatus(str, Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"

class Category(BaseModel, table=True):
    __tablename__ = "categories"
    
    name: str = Field(max_length=100, unique=True)
    description: Optional[str] = None
    
    # Relationships
    posts: List["BlogPost"] = Relationship(back_populates="category")

class BlogPost(BaseModel, table=True):
    __tablename__ = "blog_posts"
    
    title: str = Field(max_length=200)
    slug: str = Field(max_length=200, unique=True)
    content: str
    excerpt: Optional[str] = Field(max_length=500)
    status: PostStatus = Field(default=PostStatus.DRAFT)
    published_at: Optional[datetime] = None
    view_count: int = Field(default=0)
    
    # Foreign keys
    category_id: Optional[int] = Field(foreign_key="categories.id")
    author_id: int = Field(foreign_key="users.id")
    
    # Relationships
    category: Optional[Category] = Relationship(back_populates="posts")
    author: "User" = Relationship()
    tags: List["Tag"] = Relationship(link_table="post_tags")
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "My First Blog Post",
                "slug": "my-first-blog-post",
                "content": "This is the content of my first blog post.",
                "excerpt": "A brief excerpt...",
                "status": "published",
                "category_id": 1,
                "author_id": 1
            }
        }

class Tag(BaseModel, table=True):
    __tablename__ = "tags"
    
    name: str = Field(max_length=50, unique=True)
    color: Optional[str] = Field(max_length=7)  # Hex color code
    
    # Relationships
    posts: List[BlogPost] = Relationship(link_table="post_tags")

# Many-to-many link table
class PostTag(BaseModel, table=True):
    __tablename__ = "post_tags"
    
    post_id: int = Field(foreign_key="blog_posts.id", primary_key=True)
    tag_id: int = Field(foreign_key="tags.id", primary_key=True)
```

### Model Features

**BaseModel includes:**
- `id` - Primary key (auto-generated)
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp
- `is_active` - Soft delete flag

**Field Types:**
```python
from sqlmodel import Field
from typing import Optional
from datetime import datetime, date
from decimal import Decimal

class Product(BaseModel, table=True):
    # String fields
    name: str = Field(max_length=200)
    description: Optional[str] = None
    
    # Numeric fields
    price: Decimal = Field(decimal_places=2)
    quantity: int = Field(default=0)
    weight: Optional[float] = None
    
    # Boolean fields
    is_featured: bool = Field(default=False)
    in_stock: bool = Field(default=True)
    
    # Date/time fields
    launch_date: Optional[date] = None
    expires_at: Optional[datetime] = None
    
    # JSON field
    metadata: Optional[dict] = Field(default_factory=dict)
    
    # Constraints
    sku: str = Field(max_length=50, unique=True, index=True)
    category: str = Field(max_length=100, index=True)
```

### Database Relationships

**One-to-Many:**
```python
class Author(BaseModel, table=True):
    name: str
    books: List["Book"] = Relationship(back_populates="author")

class Book(BaseModel, table=True):
    title: str
    author_id: int = Field(foreign_key="authors.id")
    author: Author = Relationship(back_populates="books")
```

**Many-to-Many:**
```python
class Student(BaseModel, table=True):
    name: str
    courses: List["Course"] = Relationship(link_table="student_courses")

class Course(BaseModel, table=True):
    name: str
    students: List[Student] = Relationship(link_table="student_courses")

class StudentCourse(BaseModel, table=True):
    __tablename__ = "student_courses"
    student_id: int = Field(foreign_key="students.id", primary_key=True)
    course_id: int = Field(foreign_key="courses.id", primary_key=True)
    enrollment_date: datetime = Field(default_factory=datetime.now)
    grade: Optional[str] = None
```

## Pydantic Schemas

Create Pydantic schemas for request/response serialization:

```python
# apps/blog/serializers.py
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from .models import PostStatus

# Base schemas
class CategoryBase(BaseModel):
    name: str = Field(..., max_length=100)
    description: Optional[str] = None

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None

class CategoryResponse(CategoryBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    class Config:
        from_attributes = True

# Blog post schemas
class BlogPostBase(BaseModel):
    title: str = Field(..., max_length=200)
    slug: str = Field(..., max_length=200)
    content: str
    excerpt: Optional[str] = Field(None, max_length=500)
    status: PostStatus = PostStatus.DRAFT
    category_id: Optional[int] = None

class BlogPostCreate(BlogPostBase):
    tag_ids: Optional[List[int]] = []
    
    @validator('slug')
    def validate_slug(cls, v):
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('Slug must contain only letters, numbers, hyphens, and underscores')
        return v.lower()

class BlogPostUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=200)
    slug: Optional[str] = Field(None, max_length=200)
    content: Optional[str] = None
    excerpt: Optional[str] = Field(None, max_length=500)
    status: Optional[PostStatus] = None
    category_id: Optional[int] = None
    tag_ids: Optional[List[int]] = None

class BlogPostResponse(BlogPostBase):
    id: int
    published_at: Optional[datetime]
    view_count: int
    created_at: datetime
    updated_at: Optional[datetime]
    
    # Nested relationships
    category: Optional[CategoryResponse] = None
    author: "UserResponse" = None
    tags: List["TagResponse"] = []
    
    class Config:
        from_attributes = True

# List response with pagination
class BlogPostListResponse(BaseModel):
    items: List[BlogPostResponse]
    total: int
    page: int
    size: int
    pages: int
```

## API Views and Endpoints

Create API endpoints using FastAPI routers:

```python
# apps/blog/views.py
from fabiplus.api import APIRouter, Depends, HTTPException, status
from fabiplus.core.database import get_session
from fabiplus.core.pagination import paginate
from fabiplus.core.auth import get_current_user
from sqlmodel import Session, select
from typing import List, Optional

from .models import BlogPost, Category, Tag
from .serializers import (
    BlogPostCreate, BlogPostUpdate, BlogPostResponse, BlogPostListResponse,
    CategoryCreate, CategoryResponse
)

router = APIRouter(prefix="/blog", tags=["Blog"])

# Blog post endpoints
@router.get("/posts", response_model=BlogPostListResponse)
async def list_posts(
    page: int = 1,
    size: int = 20,
    status: Optional[str] = None,
    category_id: Optional[int] = None,
    search: Optional[str] = None,
    session: Session = Depends(get_session)
):
    """List blog posts with filtering and pagination"""
    query = select(BlogPost)
    
    # Apply filters
    if status:
        query = query.where(BlogPost.status == status)
    if category_id:
        query = query.where(BlogPost.category_id == category_id)
    if search:
        query = query.where(BlogPost.title.contains(search))
    
    # Order by creation date (newest first)
    query = query.order_by(BlogPost.created_at.desc())
    
    # Paginate results
    return await paginate(session, query, page=page, size=size)

@router.post("/posts", response_model=BlogPostResponse, status_code=status.HTTP_201_CREATED)
async def create_post(
    post_data: BlogPostCreate,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Create a new blog post"""
    # Create post
    post = BlogPost(**post_data.dict(exclude={"tag_ids"}), author_id=current_user.id)
    session.add(post)
    session.commit()
    session.refresh(post)
    
    # Add tags if provided
    if post_data.tag_ids:
        tags = session.exec(select(Tag).where(Tag.id.in_(post_data.tag_ids))).all()
        post.tags = tags
        session.commit()
    
    return post

@router.get("/posts/{post_id}", response_model=BlogPostResponse)
async def get_post(
    post_id: int,
    session: Session = Depends(get_session)
):
    """Get a specific blog post"""
    post = session.get(BlogPost, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    
    # Increment view count
    post.view_count += 1
    session.commit()
    
    return post

@router.put("/posts/{post_id}", response_model=BlogPostResponse)
async def update_post(
    post_id: int,
    post_data: BlogPostUpdate,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Update a blog post"""
    post = session.get(BlogPost, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    
    # Check permissions (author or admin)
    if post.author_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this post"
        )
    
    # Update fields
    update_data = post_data.dict(exclude_unset=True, exclude={"tag_ids"})
    for field, value in update_data.items():
        setattr(post, field, value)
    
    # Update tags if provided
    if post_data.tag_ids is not None:
        tags = session.exec(select(Tag).where(Tag.id.in_(post_data.tag_ids))).all()
        post.tags = tags
    
    session.commit()
    session.refresh(post)
    return post

@router.delete("/posts/{post_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_post(
    post_id: int,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Delete a blog post"""
    post = session.get(BlogPost, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    
    # Check permissions
    if post.author_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this post"
        )
    
    session.delete(post)
    session.commit()

# Category endpoints
@router.get("/categories", response_model=List[CategoryResponse])
async def list_categories(session: Session = Depends(get_session)):
    """List all categories"""
    categories = session.exec(select(Category)).all()
    return categories

@router.post("/categories", response_model=CategoryResponse, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_data: CategoryCreate,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Create a new category (admin only)"""
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Staff access required"
        )
    
    category = Category(**category_data.dict())
    session.add(category)
    session.commit()
    session.refresh(category)
    return category
```

## CRUD Operations

FABI+ provides built-in CRUD operations for rapid development:

```python
# apps/blog/crud.py
from fabiplus.core.crud import CRUDBase
from .models import BlogPost, Category
from .serializers import BlogPostCreate, BlogPostUpdate

class BlogPostCRUD(CRUDBase[BlogPost, BlogPostCreate, BlogPostUpdate]):
    def get_by_slug(self, session: Session, slug: str) -> Optional[BlogPost]:
        """Get post by slug"""
        return session.exec(select(BlogPost).where(BlogPost.slug == slug)).first()
    
    def get_published(self, session: Session, skip: int = 0, limit: int = 100):
        """Get published posts"""
        return session.exec(
            select(BlogPost)
            .where(BlogPost.status == "published")
            .offset(skip)
            .limit(limit)
        ).all()
    
    def increment_views(self, session: Session, post_id: int) -> Optional[BlogPost]:
        """Increment post view count"""
        post = self.get(session, post_id)
        if post:
            post.view_count += 1
            session.commit()
            session.refresh(post)
        return post

# Create CRUD instances
blog_post_crud = BlogPostCRUD(BlogPost)
category_crud = CRUDBase(Category)

# Use in views
@router.get("/posts/{post_id}")
async def get_post(post_id: int, session: Session = Depends(get_session)):
    post = blog_post_crud.get(session, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    
    # Increment view count
    blog_post_crud.increment_views(session, post_id)
    return post
```

## Custom Endpoints

Create custom endpoints for specific business logic:

```python
# Custom endpoints in views.py
@router.get("/posts/popular", response_model=List[BlogPostResponse])
async def get_popular_posts(
    limit: int = 10,
    session: Session = Depends(get_session)
):
    """Get most popular posts by view count"""
    posts = session.exec(
        select(BlogPost)
        .where(BlogPost.status == "published")
        .order_by(BlogPost.view_count.desc())
        .limit(limit)
    ).all()
    return posts

@router.get("/posts/recent", response_model=List[BlogPostResponse])
async def get_recent_posts(
    days: int = 7,
    session: Session = Depends(get_session)
):
    """Get recent posts from the last N days"""
    from datetime import datetime, timedelta

    cutoff_date = datetime.now() - timedelta(days=days)
    posts = session.exec(
        select(BlogPost)
        .where(BlogPost.created_at >= cutoff_date)
        .where(BlogPost.status == "published")
        .order_by(BlogPost.created_at.desc())
    ).all()
    return posts

@router.post("/posts/{post_id}/publish")
async def publish_post(
    post_id: int,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Publish a draft post"""
    post = session.get(BlogPost, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    if post.author_id != current_user.id and not current_user.is_staff:
        raise HTTPException(status_code=403, detail="Not authorized")

    post.status = PostStatus.PUBLISHED
    post.published_at = datetime.now()
    session.commit()

    return {"message": "Post published successfully"}

@router.get("/stats/overview")
async def get_blog_stats(
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    """Get blog statistics (admin only)"""
    if not current_user.is_staff:
        raise HTTPException(status_code=403, detail="Staff access required")

    total_posts = session.exec(select(func.count(BlogPost.id))).first()
    published_posts = session.exec(
        select(func.count(BlogPost.id)).where(BlogPost.status == "published")
    ).first()
    total_views = session.exec(select(func.sum(BlogPost.view_count))).first() or 0

    return {
        "total_posts": total_posts,
        "published_posts": published_posts,
        "draft_posts": total_posts - published_posts,
        "total_views": total_views
    }
```

## Authentication

FABI+ provides built-in authentication with OAuth2 and JWT support:

```python
# Authentication in views
from fabiplus.core.auth import get_current_user, get_current_active_user

@router.get("/profile")
async def get_profile(current_user = Depends(get_current_user)):
    """Get current user profile"""
    return current_user

@router.post("/posts", dependencies=[Depends(get_current_active_user)])
async def create_post(post_data: BlogPostCreate):
    """Create post (requires active user)"""
    # Implementation here
    pass

# Optional authentication
@router.get("/posts/{post_id}")
async def get_post(
    post_id: int,
    current_user = Depends(get_current_user, use_cache=False)
):
    """Get post (optional authentication)"""
    # current_user will be None if not authenticated
    if current_user:
        # User-specific logic
        pass

    # Public logic
    pass
```

### Custom Authentication

Create custom authentication logic:

```python
# Custom authentication dependency
from fabiplus.core.auth import AuthBackend

async def get_admin_user(current_user = Depends(get_current_user)):
    """Require admin user"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

async def get_staff_user(current_user = Depends(get_current_user)):
    """Require staff user"""
    if not current_user.is_staff:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Staff access required"
        )
    return current_user

# Use in endpoints
@router.delete("/posts/{post_id}", dependencies=[Depends(get_admin_user)])
async def delete_post(post_id: int):
    """Delete post (admin only)"""
    pass
```

## Permissions and Authorization

Implement role-based access control:

```python
# Permission decorators
from functools import wraps
from fabiplus.core.permissions import Permission, has_permission

def require_permission(permission: str):
    """Decorator to require specific permission"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user or not has_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{permission}' required"
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Model-level permissions
class BlogPostPermissions:
    @staticmethod
    def can_create(user) -> bool:
        return user.is_authenticated

    @staticmethod
    def can_read(user, post) -> bool:
        return post.status == "published" or user == post.author or user.is_staff

    @staticmethod
    def can_update(user, post) -> bool:
        return user == post.author or user.is_staff

    @staticmethod
    def can_delete(user, post) -> bool:
        return user == post.author or user.is_superuser

# Use in views
@router.put("/posts/{post_id}")
async def update_post(
    post_id: int,
    post_data: BlogPostUpdate,
    session: Session = Depends(get_session),
    current_user = Depends(get_current_user)
):
    post = session.get(BlogPost, post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    if not BlogPostPermissions.can_update(current_user, post):
        raise HTTPException(status_code=403, detail="Not authorized")

    # Update logic here
    pass
```

## Request/Response Handling

### Request Validation

```python
from pydantic import BaseModel, validator, Field
from typing import List, Optional

class BlogPostCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    content: str = Field(..., min_length=10)
    tags: Optional[List[str]] = Field(default=[], max_items=10)

    @validator('title')
    def validate_title(cls, v):
        if not v.strip():
            raise ValueError('Title cannot be empty')
        return v.strip()

    @validator('tags')
    def validate_tags(cls, v):
        if v:
            # Remove duplicates and empty tags
            v = list(set(tag.strip() for tag in v if tag.strip()))
        return v

# Custom validation in endpoints
@router.post("/posts")
async def create_post(post_data: BlogPostCreate):
    # Additional validation
    if len(post_data.content.split()) < 50:
        raise HTTPException(
            status_code=400,
            detail="Post content must be at least 50 words"
        )

    # Create post logic
    pass
```

### Response Formatting

```python
from fabiplus.core.responses import APIResponse

@router.get("/posts/{post_id}")
async def get_post(post_id: int):
    post = get_post_or_404(post_id)

    return APIResponse(
        data=post,
        message="Post retrieved successfully",
        status_code=200
    )

# Custom response models
class APIResponseModel(BaseModel):
    success: bool
    data: Optional[dict] = None
    message: str
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.now)

@router.post("/posts", response_model=APIResponseModel)
async def create_post(post_data: BlogPostCreate):
    try:
        post = create_post_logic(post_data)
        return APIResponseModel(
            success=True,
            data=post.dict(),
            message="Post created successfully"
        )
    except ValidationError as e:
        return APIResponseModel(
            success=False,
            message="Validation failed",
            errors=[str(error) for error in e.errors()]
        )
```

## Error Handling

Implement comprehensive error handling:

```python
from fabiplus.core.exceptions import FABIPlusException

# Custom exceptions
class BlogPostNotFound(FABIPlusException):
    def __init__(self, post_id: int):
        super().__init__(f"Blog post with ID {post_id} not found", status_code=404)

class BlogPostValidationError(FABIPlusException):
    def __init__(self, message: str):
        super().__init__(message, status_code=400)

# Exception handlers
@router.exception_handler(BlogPostNotFound)
async def blog_post_not_found_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.message, "type": "blog_post_not_found"}
    )

# Global error handling
from fabiplus.core.middleware import ErrorHandlingMiddleware

app.add_middleware(ErrorHandlingMiddleware)

# Try-catch in endpoints
@router.post("/posts")
async def create_post(post_data: BlogPostCreate):
    try:
        # Validation
        if not post_data.title.strip():
            raise BlogPostValidationError("Title cannot be empty")

        # Create post
        post = BlogPost(**post_data.dict())
        session.add(post)
        session.commit()

        return post

    except IntegrityError as e:
        session.rollback()
        if "unique constraint" in str(e).lower():
            raise BlogPostValidationError("A post with this slug already exists")
        raise HTTPException(status_code=500, detail="Database error")

    except Exception as e:
        session.rollback()
        logger.error(f"Unexpected error creating post: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
```

## API Documentation

FABI+ automatically generates OpenAPI documentation:

```python
# Enhanced documentation with examples
@router.post(
    "/posts",
    response_model=BlogPostResponse,
    status_code=201,
    summary="Create a new blog post",
    description="Create a new blog post with title, content, and optional metadata.",
    response_description="The created blog post",
    responses={
        201: {"description": "Post created successfully"},
        400: {"description": "Validation error"},
        401: {"description": "Authentication required"},
        403: {"description": "Insufficient permissions"}
    }
)
async def create_post(
    post_data: BlogPostCreate = Body(
        ...,
        example={
            "title": "My Amazing Blog Post",
            "slug": "my-amazing-blog-post",
            "content": "This is the content of my amazing blog post...",
            "excerpt": "A brief excerpt about the post",
            "status": "draft",
            "category_id": 1,
            "tag_ids": [1, 2, 3]
        }
    ),
    current_user = Depends(get_current_user)
):
    """
    Create a new blog post.

    - **title**: The post title (required, max 200 characters)
    - **slug**: URL-friendly version of the title (required, unique)
    - **content**: The main post content (required)
    - **excerpt**: Brief summary (optional, max 500 characters)
    - **status**: Post status (draft, published, archived)
    - **category_id**: Category ID (optional)
    - **tag_ids**: List of tag IDs (optional)
    """
    # Implementation here
    pass

# Custom OpenAPI schema
from fastapi.openapi.utils import get_openapi

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title="Blog API",
        version="1.0.0",
        description="A comprehensive blog API built with FABI+",
        routes=app.routes,
    )

    # Add custom info
    openapi_schema["info"]["contact"] = {
        "name": "API Support",
        "email": "<EMAIL>"
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi
```

## Testing APIs

Write comprehensive tests for your APIs:

```python
# tests/test_blog_api.py
import pytest
from fastapi.testclient import TestClient
from fabiplus.core.testing import TestCase

class TestBlogAPI(TestCase):
    def setUp(self):
        self.client = TestClient(app)
        self.user = self.create_user(username="testuser")
        self.token = self.get_auth_token(self.user)
        self.headers = {"Authorization": f"Bearer {self.token}"}

    def test_create_post(self):
        """Test creating a blog post"""
        post_data = {
            "title": "Test Post",
            "slug": "test-post",
            "content": "This is a test post content.",
            "status": "draft"
        }

        response = self.client.post(
            "/api/v1/blog/posts",
            json=post_data,
            headers=self.headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["title"] == post_data["title"]
        assert data["author_id"] == self.user.id

    def test_list_posts(self):
        """Test listing blog posts"""
        # Create test posts
        self.create_post(title="Post 1", status="published")
        self.create_post(title="Post 2", status="draft")

        response = self.client.get("/api/v1/blog/posts")

        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) == 2
        assert data["total"] == 2

    def test_get_post_not_found(self):
        """Test getting non-existent post"""
        response = self.client.get("/api/v1/blog/posts/999")

        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    def test_update_post_unauthorized(self):
        """Test updating post without permission"""
        other_user = self.create_user(username="otheruser")
        post = self.create_post(author=other_user)

        response = self.client.put(
            f"/api/v1/blog/posts/{post.id}",
            json={"title": "Updated Title"},
            headers=self.headers
        )

        assert response.status_code == 403
```

---

**Previous**: [← Project Management](05-project-management.md) | **Next**: [Admin Interface →](07-admin-interface.md)
