# Contributing to FABI+

Thank you for your interest in contributing to FABI+! This guide will help you get started with development, understand our code standards, and contribute effectively to the project.

## Table of Contents

- [Development Setup](#development-setup)
- [Code Standards](#code-standards)
- [Testing Guidelines](#testing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Issue Reporting](#issue-reporting)
- [Documentation](#documentation)
- [Release Process](#release-process)
- [Community Guidelines](#community-guidelines)

## Development Setup

### Prerequisites

- Python 3.8 or higher
- Poetry for dependency management
- Git for version control
- PostgreSQL or SQLite for database

### Setting Up Development Environment

1. **Fork and Clone the Repository**

```bash
# Fork the repository on GitHub, then clone your fork
git clone https://github.com/yourusername/new_fabi.git
cd new_fabi

# Add upstream remote
git remote add upstream https://github.com/original/new_fabi.git
```

2. **Install Dependencies**

```bash
# Install Poetry if you haven't already
curl -sSL https://install.python-poetry.org | python3 -

# Install project dependencies
poetry install --with dev,test

# Activate virtual environment
poetry shell
```

3. **Setup Pre-commit Hooks**

```bash
# Install pre-commit hooks
pre-commit install

# Run hooks on all files (optional)
pre-commit run --all-files
```

4. **Configure Environment**

```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your settings
# DATABASE_URL=postgresql://user:pass@localhost/fabiplus_dev
# SECRET_KEY=your-secret-key-here
```

5. **Setup Database**

```bash
# Create database (PostgreSQL example)
createdb fabiplus_dev

# Run migrations
poetry run python -m fabiplus.cli db migrate
```

6. **Run Tests**

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=fabiplus --cov-report=html

# Run specific test file
poetry run pytest fabiplus/tests/test_models.py
```

### Development Workflow

1. **Create Feature Branch**

```bash
# Update main branch
git checkout main
git pull upstream main

# Create feature branch
git checkout -b feature/your-feature-name
```

2. **Make Changes**

- Write code following our standards
- Add tests for new functionality
- Update documentation as needed
- Run tests locally

3. **Commit Changes**

```bash
# Stage changes
git add .

# Commit with descriptive message
git commit -m "feat: add user authentication system

- Implement JWT-based authentication
- Add user registration and login endpoints
- Include password hashing and validation
- Add comprehensive tests for auth flows"
```

4. **Push and Create PR**

```bash
# Push to your fork
git push origin feature/your-feature-name

# Create pull request on GitHub
```

## Code Standards

### Python Code Style

We follow PEP 8 with some modifications. Use the provided tools for consistency:

```bash
# Format code with Black
black fabiplus/

# Sort imports with isort
isort fabiplus/

# Check code style with flake8
flake8 fabiplus/

# Type checking with mypy
mypy fabiplus/
```

### Code Quality Guidelines

1. **Type Hints**: Use type hints for all function parameters and return values

```python
from typing import List, Optional, Dict, Any

def get_user_posts(user_id: int, limit: Optional[int] = None) -> List[Dict[str, Any]]:
    """Get posts for a specific user."""
    # Implementation here
    pass
```

2. **Docstrings**: Use Google-style docstrings

```python
def create_blog_post(title: str, content: str, author_id: int) -> BlogPost:
    """Create a new blog post.
    
    Args:
        title: The post title
        content: The post content
        author_id: ID of the post author
        
    Returns:
        The created blog post instance
        
    Raises:
        ValidationError: If title or content is invalid
        UserNotFoundError: If author doesn't exist
    """
    # Implementation here
    pass
```

3. **Error Handling**: Use specific exceptions and proper error handling

```python
from fabiplus.core.exceptions import ValidationError, NotFoundError

def get_user_by_id(user_id: int) -> User:
    """Get user by ID."""
    try:
        user = User.get(user_id)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")
        return user
    except DatabaseError as e:
        logger.error(f"Database error getting user {user_id}: {e}")
        raise
```

4. **Logging**: Use structured logging

```python
import logging

logger = logging.getLogger(__name__)

def process_user_data(user_data: dict):
    """Process user data."""
    logger.info("Processing user data", extra={
        "user_id": user_data.get("id"),
        "action": "process_user_data"
    })
    
    try:
        # Process data
        result = do_processing(user_data)
        logger.info("User data processed successfully", extra={
            "user_id": user_data.get("id"),
            "result_count": len(result)
        })
        return result
    except Exception as e:
        logger.error("Failed to process user data", extra={
            "user_id": user_data.get("id"),
            "error": str(e)
        })
        raise
```

### File Organization

```
fabiplus/
├── core/                 # Core framework components
│   ├── __init__.py
│   ├── models.py        # Base model classes
│   ├── database.py      # Database management
│   ├── cache.py         # Caching system
│   ├── auth.py          # Authentication
│   └── exceptions.py    # Custom exceptions
├── admin/               # Admin interface
│   ├── __init__.py
│   ├── views.py         # Admin views
│   ├── forms.py         # Admin forms
│   └── templates/       # Admin templates
├── cli/                 # CLI commands
│   ├── __init__.py
│   ├── commands/        # Command modules
│   └── utils.py         # CLI utilities
├── tests/               # Test suite
│   ├── __init__.py
│   ├── conftest.py      # Test configuration
│   ├── test_models.py   # Model tests
│   └── test_api.py      # API tests
└── templates/           # Project templates
    ├── project/         # Project templates
    └── app/             # App templates
```

## Testing Guidelines

### Test Structure

We use pytest for testing with the following structure:

```python
# fabiplus/tests/test_models.py
import pytest
from fabiplus.core.models import User
from fabiplus.core.database import get_session

class TestUserModel:
    """Test cases for User model."""
    
    def test_create_user(self, db_session):
        """Test user creation."""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "securepassword"
        }
        
        user = User.create(**user_data)
        
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.check_password("securepassword")
    
    def test_user_validation(self):
        """Test user validation."""
        with pytest.raises(ValidationError):
            User.create(username="", email="invalid-email")
    
    @pytest.mark.asyncio
    async def test_async_user_operations(self, async_db_session):
        """Test async user operations."""
        user = await User.async_create(
            username="asyncuser",
            email="<EMAIL>"
        )
        
        assert user.id is not None
        
        found_user = await User.async_get(user.id)
        assert found_user.username == "asyncuser"
```

### Test Fixtures

Use fixtures for common test setup:

```python
# fabiplus/tests/conftest.py
import pytest
from fabiplus.core.database import get_session
from fabiplus.core.models import User

@pytest.fixture
def db_session():
    """Provide database session for tests."""
    with get_session() as session:
        yield session
        session.rollback()

@pytest.fixture
def test_user(db_session):
    """Create test user."""
    user = User.create(
        username="testuser",
        email="<EMAIL>",
        password="testpass"
    )
    db_session.add(user)
    db_session.commit()
    return user

@pytest.fixture
def authenticated_client(test_user):
    """Provide authenticated test client."""
    from fastapi.testclient import TestClient
    from fabiplus.main import app
    
    client = TestClient(app)
    
    # Login and get token
    response = client.post("/auth/login", json={
        "username": "testuser",
        "password": "testpass"
    })
    token = response.json()["access_token"]
    
    # Set authorization header
    client.headers.update({"Authorization": f"Bearer {token}"})
    
    return client
```

### Test Coverage

Maintain high test coverage (minimum 85%):

```bash
# Run tests with coverage
poetry run pytest --cov=fabiplus --cov-report=html --cov-report=term

# View coverage report
open htmlcov/index.html
```

### Integration Tests

Write integration tests for API endpoints:

```python
# fabiplus/tests/test_api.py
import pytest
from fastapi.testclient import TestClient

class TestBlogAPI:
    """Integration tests for blog API."""
    
    def test_create_post(self, authenticated_client):
        """Test creating a blog post."""
        post_data = {
            "title": "Test Post",
            "content": "This is a test post.",
            "status": "draft"
        }
        
        response = authenticated_client.post("/api/v1/posts", json=post_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "Test Post"
        assert data["status"] == "draft"
    
    def test_list_posts(self, authenticated_client, test_posts):
        """Test listing blog posts."""
        response = authenticated_client.get("/api/v1/posts")
        
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert len(data["items"]) > 0
```

## Pull Request Process

### Before Submitting

1. **Run Full Test Suite**

```bash
# Run all tests
poetry run pytest

# Run linting
poetry run flake8 fabiplus/
poetry run black --check fabiplus/
poetry run isort --check-only fabiplus/

# Run type checking
poetry run mypy fabiplus/
```

2. **Update Documentation**

- Update relevant documentation files
- Add docstrings to new functions/classes
- Update CHANGELOG.md if applicable

3. **Test Your Changes**

- Test manually in a development environment
- Ensure all existing tests pass
- Add new tests for new functionality

### PR Guidelines

1. **Title and Description**

- Use clear, descriptive titles
- Follow conventional commit format: `feat:`, `fix:`, `docs:`, etc.
- Provide detailed description of changes
- Reference related issues

2. **Code Review Checklist**

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or properly documented)
- [ ] Performance impact considered
- [ ] Security implications reviewed

3. **PR Template**

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
```

## Issue Reporting

### Bug Reports

Use the bug report template:

```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- FABI+ version:
- Python version:
- OS:
- Database:

**Additional Context**
Any other relevant information
```

### Feature Requests

Use the feature request template:

```markdown
**Feature Description**
Clear description of the proposed feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should this feature work?

**Alternatives Considered**
Other approaches you've considered

**Additional Context**
Any other relevant information
```

## Documentation

### Writing Documentation

1. **Use Clear Language**: Write for developers of all skill levels
2. **Include Examples**: Provide practical code examples
3. **Keep Updated**: Update docs when code changes
4. **Cross-Reference**: Link related sections

### Documentation Structure

```markdown
# Section Title

Brief introduction to the section.

## Subsection

Detailed explanation with examples.

### Code Example

```python
# Example code with comments
def example_function():
    """Example function."""
    pass
```

### Configuration

```yaml
# Example configuration
setting: value
```

## See Also

- [Related Section](link)
- [External Resource](link)
```

## Release Process

### Version Numbering

We follow Semantic Versioning (SemVer):

- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

1. **Update Version Numbers**
2. **Update CHANGELOG.md**
3. **Run Full Test Suite**
4. **Update Documentation**
5. **Create Release PR**
6. **Tag Release**
7. **Publish to PyPI**

## Community Guidelines

### Code of Conduct

- Be respectful and inclusive
- Welcome newcomers
- Focus on constructive feedback
- Respect different viewpoints
- Help others learn and grow

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and discussions
- **Discord**: Real-time chat and community support

### Getting Help

- Check existing documentation
- Search GitHub issues
- Ask in GitHub Discussions
- Join our Discord community

---

**Previous**: [← Core Architecture](09-core-architecture.md) | **Home**: [Introduction →](01-introduction.md)
