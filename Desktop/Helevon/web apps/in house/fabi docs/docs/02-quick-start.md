# Quick Start Guide

Get up and running with FABI+ in under 5 minutes! This guide will walk you through creating your first FABI+ project and building a simple blog API.

## Prerequisites

Before starting, ensure you have:
- Python 3.10 or higher installed
- Poetry package manager installed
- Basic knowledge of Python and APIs

### Installing Poetry

If you don't have Poetry installed:

```bash
# On macOS/Linux
curl -sSL https://install.python-poetry.org | python3 -

# On Windows (PowerShell)
(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -

# Add Poetry to your PATH (follow the instructions shown after installation)
```

## Step 1: Install FABI+

Install FABI+ framework using pip:

```bash
pip install fabiplus
```

Verify the installation:

```bash
fabiplus --help
```

You should see the FABI+ CLI help output with available commands.

## Step 2: Create Your First Project

Create a new FABI+ project called "myblog":

```bash
fabiplus project startproject myblog
```

This command creates a complete project structure with:
- Project configuration files
- Poetry setup with dependencies
- Environment configuration
- Basic app structure
- Example models and views

Navigate to your project directory:

```bash
cd myblog
```

## Step 3: Install Dependencies

Install the project dependencies using Poetry:

```bash
poetry install
```

This installs all required dependencies in a virtual environment.

## Step 4: Create Your First App

Create a blog app within your project:

```bash
fabiplus app startapp blog
```

This creates a new app in the `apps/blog/` directory with:
- `models.py` - Database models
- `views.py` - API endpoints
- `admin.py` - Admin interface configuration
- `serializers.py` - Pydantic schemas
- `urls.py` - URL routing
- `tests.py` - Test cases

## Step 5: Set Up the Database

Initialize the database and run migrations:

```bash
# Create migration files
fabiplus db makemigrations

# Apply migrations to create tables
fabiplus db migrate
```

## Step 6: Create a Superuser

Create an admin user to access the admin interface:

```bash
fabiplus user create --username admin --email <EMAIL> --superuser
```

You'll be prompted to enter a password. Choose a secure password for your admin account.

## Step 7: Start the Development Server

Start the FABI+ development server:

```bash
fabiplus server run
```

The server will start on `http://127.0.0.1:8000` by default.

## Step 8: Explore Your Application

Open your browser and visit these URLs:

### 🔗 API Documentation
Visit `http://127.0.0.1:8000/docs` to see the interactive API documentation powered by Swagger UI.

### 🛡️ Admin Interface
Visit `http://127.0.0.1:8000/admin` to access the Django-style admin interface. Log in with the superuser credentials you created.

### 📊 API Endpoints
Visit `http://127.0.0.1:8000/api/v1/` to see the available API endpoints.

## Step 9: Add Your First Model

Let's create a simple blog post model. Edit `apps/blog/models.py`:

```python
from fabiplus.core.models import BaseModel
from sqlmodel import Field
from typing import Optional
from datetime import datetime

class BlogPost(BaseModel, table=True):
    __tablename__ = "blog_posts"
    
    title: str = Field(max_length=200)
    content: str
    author: str = Field(max_length=100)
    published: bool = Field(default=False)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "My First Blog Post",
                "content": "This is the content of my first blog post.",
                "author": "John Doe",
                "published": True
            }
        }
```

## Step 10: Register the Model in Admin

Edit `apps/blog/admin.py` to register your model:

```python
from fabiplus.admin import admin
from .models import BlogPost

# Register the BlogPost model with the admin
admin.register(BlogPost)
```

## Step 11: Create API Views

Edit `apps/blog/views.py` to create API endpoints:

```python
from fabiplus.api import APIRouter, Depends
from fabiplus.core.models import ModelRegistry
from .models import BlogPost
from .serializers import BlogPostCreate, BlogPostUpdate, BlogPostResponse
from typing import List

router = APIRouter(prefix="/blog", tags=["Blog"])

@router.get("/posts", response_model=List[BlogPostResponse])
async def list_posts():
    """Get all blog posts"""
    with ModelRegistry.get_session() as session:
        posts = session.query(BlogPost).all()
        return posts

@router.post("/posts", response_model=BlogPostResponse)
async def create_post(post_data: BlogPostCreate):
    """Create a new blog post"""
    with ModelRegistry.get_session() as session:
        post = BlogPost(**post_data.dict())
        session.add(post)
        session.commit()
        session.refresh(post)
        return post

@router.get("/posts/{post_id}", response_model=BlogPostResponse)
async def get_post(post_id: int):
    """Get a specific blog post"""
    with ModelRegistry.get_session() as session:
        post = session.get(BlogPost, post_id)
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        return post
```

## Step 12: Update Database Schema

After adding your model, create and apply a new migration:

```bash
# Create migration for the new model
fabiplus db makemigrations -m "Add BlogPost model"

# Apply the migration
fabiplus db migrate
```

## Step 13: Test Your API

Restart the development server:

```bash
fabiplus server run
```

Now you can:

1. **Visit the admin interface** at `http://127.0.0.1:8000/admin` and create blog posts through the web interface
2. **Use the API documentation** at `http://127.0.0.1:8000/docs` to test your endpoints
3. **Make API calls** directly:

```bash
# Create a blog post
curl -X POST "http://127.0.0.1:8000/api/v1/blog/posts" \
     -H "Content-Type: application/json" \
     -d '{
       "title": "My First Post",
       "content": "Hello, FABI+ World!",
       "author": "Developer",
       "published": true
     }'

# Get all posts
curl "http://127.0.0.1:8000/api/v1/blog/posts"
```

## What's Next?

Congratulations! You've successfully created your first FABI+ application with:
- ✅ A complete project structure
- ✅ Database models and migrations
- ✅ API endpoints with automatic documentation
- ✅ Admin interface for content management
- ✅ User authentication system

### Continue Learning

- **[Installation & Setup](03-installation-setup.md)** - Detailed installation and configuration
- **[CLI Commands Reference](04-cli-commands.md)** - Complete CLI documentation
- **[Project Management](05-project-management.md)** - Advanced project configuration
- **[API Development](06-api-development.md)** - Building complex APIs
- **[Admin Interface](07-admin-interface.md)** - Customizing the admin panel

### Common Next Steps

1. **Add Authentication** to your API endpoints
2. **Create more models** and relationships
3. **Customize the admin interface** with custom views
4. **Add file uploads** using the media system
5. **Deploy to production** with Docker

### Getting Help

- Check the [CLI Commands Reference](04-cli-commands.md) for all available commands
- Visit the [Core Architecture](09-core-architecture.md) guide to understand the framework internals
- See the [Contributing Guide](10-contributing.md) to contribute to the project

---

**Previous**: [← Introduction](01-introduction.md) | **Next**: [Installation & Setup →](03-installation-setup.md)
