# CLI Commands Reference

FABI+ provides a comprehensive command-line interface with over 40 commands organized into logical groups. This reference covers all available commands with syntax, options, and examples.

## Table of Contents

- [Global Commands](#global-commands)
- [Project Commands](#project-commands)
- [App Commands](#app-commands)
- [Server Commands](#server-commands)
- [Database Commands](#database-commands)
- [User Commands](#user-commands)
- [Cache Commands](#cache-commands)
- [Development Commands](#development-commands)

## Global Commands

### `fabiplus version`
Show FABI+ version information.

```bash
fabiplus version
```

**Output:**
```
FABI+ Framework v0.1.0
```

### `fabiplus info`
Display comprehensive system and framework information.

```bash
fabiplus info
```

**Output:**
```
System Information:
• Python: 3.11.0
• Platform: Linux 5.15.0
• Architecture: x86_64

FABI+ Framework:
• Version: 0.1.0
• Location: /path/to/fabiplus

Available Commands:
• fabiplus project startproject - Create new project
• fabiplus app startapp - Create new app
• fabiplus server run - Run development server
• fabiplus db migrate - Run database migrations
• fabiplus user create - Create superuser
```

### `fabiplus quickstart`
Display a quick start guide with essential commands.

```bash
fabiplus quickstart
```

## Project Commands

### `fabiplus project startproject`
Create a new FABI+ project with complete structure.

**Syntax:**
```bash
fabiplus project startproject <name> [OPTIONS]
```

**Arguments:**
- `name` - Project name (must be a valid Python identifier)

**Options:**
- `--dir, -d <directory>` - Directory to create project in
- `--template, -t <template>` - Project template to use (default: "default")
- `--orm, -o <orm>` - ORM backend (sqlmodel, sqlalchemy)
- `--auth, -a <auth>` - Authentication backend (oauth2, jwt)
- `--show-admin-routes` - Show admin routes in API docs
- `--force, -f` - Overwrite existing directory
- `--docker` - Include Docker files

**Examples:**
```bash
# Basic project creation
fabiplus project startproject myblog

# Create with PostgreSQL and Docker
fabiplus project startproject myapi --orm sqlalchemy --docker

# Create in specific directory
fabiplus project startproject webapp --dir /path/to/projects

# Create with JWT authentication
fabiplus project startproject api --auth jwt --show-admin-routes
```

**Created Structure:**
```
myblog/
├── myblog/
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── apps/core/
├── manage.py
├── pyproject.toml
├── .env.example
└── README.md
```

### `fabiplus project init`
Initialize FABI+ in an existing directory.

**Syntax:**
```bash
fabiplus project init [OPTIONS]
```

**Options:**
- `--force, -f` - Initialize in non-empty directory

**Example:**
```bash
cd existing-project
fabiplus project init --force
```

### `fabiplus project list-templates`
List available project templates.

```bash
fabiplus project list-templates
```

**Available Templates:**
- `default` - Standard project with all features
- `minimal` - Minimal project structure
- `api` - API-focused project
- `microservice` - Microservice template
- `monolith` - Monolithic application template

### `fabiplus project list-orms`
List available ORM backends.

```bash
fabiplus project list-orms
```

**Available ORMs:**
- `sqlmodel` - Modern, type-safe ORM (default)
- `sqlalchemy` - Mature, feature-rich ORM
- `tortoise` - Async-native ORM (coming soon)

## App Commands

### `fabiplus app startapp`
Create a new app within a FABI+ project.

**Syntax:**
```bash
fabiplus app startapp <name> [OPTIONS]
```

**Arguments:**
- `name` - App name (must be a valid Python identifier)

**Options:**
- `--dir, -d <directory>` - Directory to create app in
- `--template, -t <template>` - App template to use
- `--force, -f` - Overwrite existing app

**Examples:**
```bash
# Create a basic app
fabiplus app startapp blog

# Create with specific template
fabiplus app startapp shop --template ecommerce

# Create in custom directory
fabiplus app startapp api --dir custom_apps
```

**Created Structure:**
```
apps/blog/
├── __init__.py
├── models.py
├── views.py
├── admin.py
├── serializers.py
├── urls.py
└── tests.py
```

### `fabiplus app list-templates`
List available app templates.

```bash
fabiplus app list-templates
```

**Available Templates:**
- `default` - Standard app with models, views, admin, and tests
- `minimal` - Minimal app structure
- `api` - API-focused app with serializers
- `crud` - Full CRUD app with all operations
- `readonly` - Read-only app for data display
- `auth` - Authentication app with user management
- `blog` - Blog app with posts and comments
- `ecommerce` - E-commerce app with products and orders

### `fabiplus app remove`
Remove an existing app from the project.

**Syntax:**
```bash
fabiplus app remove <name> [OPTIONS]
```

**Arguments:**
- `name` - App name to remove

**Options:**
- `--force, -f` - Force removal without confirmation

**Example:**
```bash
fabiplus app remove old_app --force
```

### `fabiplus app generate`
Generate model, views, admin, and tests for an app.

**Syntax:**
```bash
fabiplus app generate <app_name> <model_name> [OPTIONS]
```

**Arguments:**
- `app_name` - Target app name
- `model_name` - Model name to generate

**Options:**
- `--fields <fields>` - Model fields (format: "name:type,name:type")
- `--admin/--no-admin` - Generate admin configuration (default: true)
- `--views/--no-views` - Generate API views (default: true)
- `--tests/--no-tests` - Generate test cases (default: true)

**Example:**
```bash
fabiplus app generate blog Post --fields "title:str,content:text,published:bool"
```

## Server Commands

### `fabiplus server run`
Run the development server.

**Syntax:**
```bash
fabiplus server run [OPTIONS]
```

**Options:**
- `--host, -h <host>` - Host to bind to (default: 127.0.0.1)
- `--port, -p <port>` - Port to bind to (default: 8000)
- `--reload/--no-reload` - Enable auto-reload (default: true)
- `--workers, -w <count>` - Number of worker processes (default: 1)
- `--log-level <level>` - Log level (debug, info, warning, error)

**Examples:**
```bash
# Basic server start
fabiplus server run

# Custom host and port
fabiplus server run --host 0.0.0.0 --port 8080

# Production mode with multiple workers
fabiplus server run --workers 4 --no-reload --log-level info

# Debug mode
fabiplus server run --log-level debug
```

### `fabiplus server check`
Check server configuration and display current settings.

```bash
fabiplus server check
```

**Output:**
```
Server Configuration:
Host: 127.0.0.1
Port: 8000
Debug: True
Environment: development
Database: sqlite:///./fabiplus.db
API Prefix: /api
Admin Enabled: True
```

## Database Commands

### `fabiplus db migrate`
Apply database migrations.

**Syntax:**
```bash
fabiplus db migrate [revision]
```

**Arguments:**
- `revision` - Specific revision to migrate to (optional)

**Examples:**
```bash
# Apply all pending migrations
fabiplus db migrate

# Migrate to specific revision
fabiplus db migrate abc123
```

### `fabiplus db makemigrations`
Create new database migration files.

**Syntax:**
```bash
fabiplus db makemigrations [OPTIONS]
```

**Options:**
- `--message, -m <message>` - Migration message

**Examples:**
```bash
# Auto-generate migrations
fabiplus db makemigrations

# With custom message
fabiplus db makemigrations -m "Add user profile model"
```

### `fabiplus db reset`
Reset database (WARNING: Deletes all data).

**Syntax:**
```bash
fabiplus db reset [OPTIONS]
```

**Options:**
- `--force, -f` - Force reset without confirmation

**Example:**
```bash
fabiplus db reset --force
```

### `fabiplus db info`
Display database information and registered models.

```bash
fabiplus db info
```

**Output:**
```
Database Information:
Database URL: sqlite:///./fabiplus.db
Echo SQL: False
Registered Models: 3

Registered Models:
• User: User
• BlogPost: BlogPost
• Category: Category
```

### `fabiplus db showmigrations`
Show migration status and history.

```bash
fabiplus db showmigrations
```

### `fabiplus db create-tables`
Create database tables from models.

```bash
fabiplus db create-tables
```

## User Commands

### `fabiplus user create`
Create a new user account.

**Syntax:**
```bash
fabiplus user create [OPTIONS]
```

**Options:**
- `--username, -u <username>` - Username
- `--email, -e <email>` - Email address
- `--password, -p <password>` - Password
- `--superuser` - Create superuser
- `--staff` - Create staff user

**Examples:**
```bash
# Interactive user creation
fabiplus user create

# Create superuser with options
fabiplus user create --username admin --email <EMAIL> --superuser

# Create staff user
fabiplus user create --username staff --email <EMAIL> --staff
```

### `fabiplus user list`
List all users in the system.

**Syntax:**
```bash
fabiplus user list [OPTIONS]
```

**Options:**
- `--staff` - Show only staff users
- `--superuser` - Show only superusers

**Examples:**
```bash
# List all users
fabiplus user list

# List only superusers
fabiplus user list --superuser
```

### `fabiplus user changepassword`
Change a user's password.

**Syntax:**
```bash
fabiplus user changepassword <username> [OPTIONS]
```

**Arguments:**
- `username` - Username to change password for

**Options:**
- `--password, -p <password>` - New password

**Example:**
```bash
fabiplus user changepassword john --password newpassword123
```

### `fabiplus user activate`
Activate a user account.

**Syntax:**
```bash
fabiplus user activate <username>
```

**Example:**
```bash
fabiplus user activate john
```

### `fabiplus user deactivate`
Deactivate a user account.

**Syntax:**
```bash
fabiplus user deactivate <username>
```

**Example:**
```bash
fabiplus user deactivate john
```

## Cache Commands

### `fabiplus cache clear`
Clear all cached data.

**Syntax:**
```bash
fabiplus cache clear [OPTIONS]
```

**Options:**
- `--yes, -y` - Skip confirmation

**Examples:**
```bash
# Clear with confirmation
fabiplus cache clear

# Clear without confirmation
fabiplus cache clear --yes
```

### `fabiplus cache list`
List cached keys and their types.

**Syntax:**
```bash
fabiplus cache list [OPTIONS]
```

**Options:**
- `--pattern <pattern>` - Filter keys by pattern
- `--limit <limit>` - Limit number of results (default: 50)

**Examples:**
```bash
# List all cached keys
fabiplus cache list

# Filter by pattern
fabiplus cache list --pattern "user:"

# Limit results
fabiplus cache list --limit 20
```

### `fabiplus cache get`
Get a specific cached value.

**Syntax:**
```bash
fabiplus cache get <key>
```

**Example:**
```bash
fabiplus cache get "user:123"
```

### `fabiplus cache delete`
Delete a specific cached key.

**Syntax:**
```bash
fabiplus cache delete <key> [OPTIONS]
```

**Arguments:**
- `key` - Cache key to delete

**Options:**
- `--yes, -y` - Skip confirmation

**Example:**
```bash
fabiplus cache delete "user:123" --yes
```

### `fabiplus cache info`
Display cache configuration and statistics.

```bash
fabiplus cache info
```

**Output:**
```
Cache Information:
Backend: memory
Default TTL: 300 seconds
Redis URL: Not configured

Cache Backend Details:
Type: MemoryCache
Status: ✅ Active

Current Statistics:
  Hits: 1250
  Misses: 89
  Sets: 456
  Deletes: 23
```

### `fabiplus cache warm`
Warm up the cache with frequently accessed data.

```bash
fabiplus cache warm
```

### `fabiplus cache benchmark`
Run cache performance benchmarks.

**Syntax:**
```bash
fabiplus cache benchmark [OPTIONS]
```

**Options:**
- `--operations <count>` - Number of operations (default: 1000)
- `--key-length <length>` - Key length (default: 10)
- `--value-size <size>` - Value size in bytes (default: 100)

**Example:**
```bash
fabiplus cache benchmark --operations 5000 --key-length 20
```

## Development Commands

### `fabiplus dev shell`
Start an interactive Python shell with FABI+ context.

```bash
fabiplus dev shell
```

**Available Objects:**
- `fabiplus` - Framework module
- `BaseModel` - Base model class
- `ModelRegistry` - Model registry
- `User` - User model
- `auth_backend` - Authentication backend
- `settings` - Framework settings
- `session` - Database session

### `fabiplus dev routes`
Display all registered routes and endpoints.

```bash
fabiplus dev routes
```

### `fabiplus dev models`
Display all registered models and their fields.

```bash
fabiplus dev models
```

## Command Examples and Workflows

### Complete Project Setup Workflow

```bash
# 1. Create new project
fabiplus project startproject myblog --docker

# 2. Navigate to project
cd myblog

# 3. Install dependencies
poetry install

# 4. Create first app
fabiplus app startapp blog

# 5. Setup database
fabiplus db makemigrations
fabiplus db migrate

# 6. Create superuser
fabiplus user create --username admin --email <EMAIL> --superuser

# 7. Start development server
fabiplus server run
```

### Development Workflow

```bash
# Create new model
fabiplus app generate blog Post --fields "title:str,content:text,published:bool"

# Create and apply migrations
fabiplus db makemigrations -m "Add Post model"
fabiplus db migrate

# Check database status
fabiplus db info

# Clear cache after changes
fabiplus cache clear --yes

# Run development server
fabiplus server run --reload
```

### Production Deployment Workflow

```bash
# Create production project
fabiplus project startproject myapi --orm sqlalchemy --docker

# Setup production database
fabiplus db create-tables

# Create admin user
fabiplus user create --username admin --superuser

# Run production server
fabiplus server run --host 0.0.0.0 --port 8000 --workers 4 --no-reload
```

## Command Help

Every command supports the `--help` flag for detailed information:

```bash
# Get help for any command
fabiplus --help
fabiplus project --help
fabiplus project startproject --help
fabiplus server run --help
```

## Troubleshooting Commands

### Check System Status
```bash
fabiplus info
fabiplus server check
fabiplus db info
fabiplus cache info
```

### Debug Database Issues
```bash
fabiplus db showmigrations
fabiplus db info
fabiplus dev models
```

### Debug Cache Issues
```bash
fabiplus cache info
fabiplus cache list
fabiplus cache benchmark
```

---

**Previous**: [← Installation & Setup](03-installation-setup.md) | **Next**: [Project Management →](05-project-management.md)