# Advanced Features

This guide covers advanced FABI+ features including multiple ORM support, caching strategies, background tasks, middleware, and performance optimization.

## Table of Contents

- [Multiple ORM Support](#multiple-orm-support)
- [Caching Strategies](#caching-strategies)
- [Background Tasks](#background-tasks)
- [Middleware](#middleware)
- [Performance Optimization](#performance-optimization)
- [Security Features](#security-features)
- [Monitoring and Logging](#monitoring-and-logging)
- [Plugin System](#plugin-system)

## Multiple ORM Support

FABI+ supports multiple ORM backends, allowing you to choose the best fit for your project.

### SQLModel (Default)

SQLModel provides type-safe database operations with Pydantic integration:

```python
# Project creation with SQLModel
fabiplus project startproject myapi --orm sqlmodel

# Model definition
from fabiplus.core.models import BaseModel
from sqlmodel import Field, Relationship
from typing import Optional, List

class User(BaseModel, table=True):
    __tablename__ = "users"
    
    username: str = Field(max_length=150, unique=True)
    email: str = Field(max_length=254, unique=True)
    is_active: bool = Field(default=True)
    
    # Relationships
    posts: List["BlogPost"] = Relationship(back_populates="author")

class BlogPost(BaseModel, table=True):
    __tablename__ = "blog_posts"
    
    title: str = Field(max_length=200)
    content: str
    author_id: int = Field(foreign_key="users.id")
    
    # Relationships
    author: User = Relationship(back_populates="posts")

# Database operations
from fabiplus.core.database import get_session

async def create_post(title: str, content: str, author_id: int):
    with get_session() as session:
        post = BlogPost(title=title, content=content, author_id=author_id)
        session.add(post)
        session.commit()
        session.refresh(post)
        return post

async def get_posts_by_author(author_id: int):
    with get_session() as session:
        return session.exec(
            select(BlogPost).where(BlogPost.author_id == author_id)
        ).all()
```

### SQLAlchemy

SQLAlchemy provides mature, feature-rich ORM capabilities:

```python
# Project creation with SQLAlchemy
fabiplus project startproject myapi --orm sqlalchemy

# Model definition
from fabiplus.core.models import BaseModel
from sqlalchemy import Column, Integer, String, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship

class User(BaseModel):
    __tablename__ = "users"
    
    username = Column(String(150), unique=True, nullable=False)
    email = Column(String(254), unique=True, nullable=False)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    posts = relationship("BlogPost", back_populates="author")

class BlogPost(BaseModel):
    __tablename__ = "blog_posts"
    
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    author_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    author = relationship("User", back_populates="posts")

# Database operations
from fabiplus.core.database import get_session
from sqlalchemy.orm import selectinload

async def get_posts_with_authors():
    with get_session() as session:
        return session.query(BlogPost).options(
            selectinload(BlogPost.author)
        ).all()

async def get_user_post_count():
    with get_session() as session:
        return session.query(
            User.username,
            func.count(BlogPost.id).label('post_count')
        ).join(BlogPost).group_by(User.id).all()
```

### Tortoise ORM (Coming Soon)

Async-native ORM for high-performance applications:

```python
# Project creation with Tortoise ORM (future)
fabiplus project startproject myapi --orm tortoise

# Model definition
from fabiplus.core.models import BaseModel
from tortoise.models import Model
from tortoise import fields

class User(BaseModel):
    username = fields.CharField(max_length=150, unique=True)
    email = fields.CharField(max_length=254, unique=True)
    is_active = fields.BooleanField(default=True)

class BlogPost(BaseModel):
    title = fields.CharField(max_length=200)
    content = fields.TextField()
    author = fields.ForeignKeyField("models.User", related_name="posts")

# Async database operations
async def create_post(title: str, content: str, author_id: int):
    return await BlogPost.create(
        title=title,
        content=content,
        author_id=author_id
    )

async def get_posts_with_authors():
    return await BlogPost.all().prefetch_related("author")
```

### ORM Configuration

Configure ORM-specific settings:

```python
# settings.py
class Settings(FABIPlusSettings):
    # ORM Backend
    ORM_BACKEND: str = "sqlmodel"  # sqlmodel, sqlalchemy, tortoise
    
    # Database settings
    DATABASE_URL: str = "postgresql://user:pass@localhost/db"
    DATABASE_ECHO: bool = False
    DATABASE_POOL_SIZE: int = 5
    DATABASE_MAX_OVERFLOW: int = 10
    
    # SQLAlchemy specific
    SQLALCHEMY_ENGINE_OPTIONS: dict = {
        "pool_pre_ping": True,
        "pool_recycle": 3600,
    }
    
    # Tortoise specific
    TORTOISE_ORM_CONFIG: dict = {
        "connections": {
            "default": DATABASE_URL
        },
        "apps": {
            "models": {
                "models": ["apps.blog.models", "apps.core.models"],
                "default_connection": "default",
            }
        }
    }
```

## Caching Strategies

FABI+ provides flexible caching strategies for improved performance.

### Memory Cache (Default)

In-memory caching for development and small applications:

```python
# Configuration
CACHE_BACKEND = "memory"
CACHE_TTL = 300  # 5 minutes

# Usage
from fabiplus.core.cache import cache

# Set cache
cache.set("user:123", user_data, ttl=600)

# Get cache
user_data = cache.get("user:123")

# Delete cache
cache.delete("user:123")

# Clear all cache
cache.clear()
```

### Redis Cache

High-performance Redis caching for production:

```python
# Configuration
CACHE_BACKEND = "redis"
REDIS_URL = "redis://localhost:6379/0"
CACHE_TTL = 300

# Install Redis client
poetry add redis hiredis

# Usage (same API as memory cache)
from fabiplus.core.cache import cache

# Cache with custom TTL
cache.set("expensive_query", result, ttl=3600)

# Cache statistics
stats = cache.stats()
print(f"Hits: {stats['hits']}, Misses: {stats['misses']}")
```

### Cache Decorators

Use decorators for automatic caching:

```python
from fabiplus.core.cache import cached

@cached(ttl=600, key_prefix="blog_posts")
async def get_popular_posts(limit: int = 10):
    """Get popular posts with caching"""
    with get_session() as session:
        return session.exec(
            select(BlogPost)
            .where(BlogPost.status == "published")
            .order_by(BlogPost.view_count.desc())
            .limit(limit)
        ).all()

@cached(ttl=3600, key_prefix="user_stats")
async def get_user_statistics(user_id: int):
    """Get user statistics with caching"""
    with get_session() as session:
        post_count = session.exec(
            select(func.count(BlogPost.id))
            .where(BlogPost.author_id == user_id)
        ).first()
        
        return {
            "post_count": post_count,
            "last_updated": datetime.now()
        }

# Cache invalidation
from fabiplus.core.cache import cache_invalidate

@cache_invalidate(patterns=["blog_posts:*", "user_stats:*"])
async def create_blog_post(post_data: BlogPostCreate):
    """Create post and invalidate related caches"""
    # Create post logic
    pass
```

### Cache Management

Manage cache through CLI and programmatically:

```bash
# CLI cache management
fabiplus cache info
fabiplus cache list --pattern "user:*"
fabiplus cache clear --yes
fabiplus cache benchmark --operations 10000

# Cache warming
fabiplus cache warm
```

```python
# Programmatic cache management
from fabiplus.core.cache import CacheManager

cache_manager = CacheManager()

# Warm up cache with frequently accessed data
async def warm_cache():
    # Cache popular posts
    popular_posts = await get_popular_posts()
    cache.set("popular_posts", popular_posts, ttl=3600)
    
    # Cache user counts
    user_count = await User.count()
    cache.set("user_count", user_count, ttl=1800)
    
    # Cache categories
    categories = await Category.all()
    cache.set("categories", categories, ttl=7200)

# Cache monitoring
def monitor_cache_performance():
    stats = cache.stats()
    hit_rate = stats['hits'] / (stats['hits'] + stats['misses']) * 100
    
    if hit_rate < 80:
        logger.warning(f"Low cache hit rate: {hit_rate:.2f}%")
    
    return {
        "hit_rate": hit_rate,
        "total_operations": stats['hits'] + stats['misses'],
        "memory_usage": cache.memory_usage()
    }
```

## Background Tasks

FABI+ supports background task processing for long-running operations.

### Celery Integration

Use Celery for distributed task processing:

```python
# Install Celery
poetry add celery redis

# celery_app.py
from celery import Celery
from fabiplus.conf import settings

celery_app = Celery(
    "fabiplus_tasks",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=["apps.blog.tasks", "apps.core.tasks"]
)

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_routes={
        "apps.blog.tasks.*": {"queue": "blog"},
        "apps.core.tasks.*": {"queue": "core"},
    }
)

# Task definition
# apps/blog/tasks.py
from celery import shared_task
from .models import BlogPost
from .services import EmailService

@shared_task
def send_post_notification(post_id: int):
    """Send notification when a post is published"""
    post = BlogPost.get(post_id)
    if post and post.status == "published":
        EmailService.send_notification(
            subject=f"New post: {post.title}",
            content=post.excerpt,
            recipients=["<EMAIL>"]
        )
    return f"Notification sent for post {post_id}"

@shared_task
def generate_post_thumbnail(post_id: int):
    """Generate thumbnail for post featured image"""
    post = BlogPost.get(post_id)
    if post and post.featured_image:
        from fabiplus.core.media import ImageProcessor
        
        processor = ImageProcessor()
        thumbnail_path = processor.create_thumbnail(
            post.featured_image,
            size=(300, 200),
            quality=85
        )
        
        post.thumbnail = thumbnail_path
        post.save()
    
    return f"Thumbnail generated for post {post_id}"

@shared_task
def cleanup_old_posts():
    """Clean up old draft posts"""
    from datetime import datetime, timedelta
    
    cutoff_date = datetime.now() - timedelta(days=30)
    old_posts = BlogPost.filter(
        status="draft",
        created_at__lt=cutoff_date
    )
    
    count = old_posts.count()
    old_posts.delete()
    
    return f"Cleaned up {count} old draft posts"

# Using tasks in views
from .tasks import send_post_notification, generate_post_thumbnail

@router.post("/posts/{post_id}/publish")
async def publish_post(post_id: int):
    post = BlogPost.get(post_id)
    post.status = "published"
    post.published_at = datetime.now()
    post.save()
    
    # Queue background tasks
    send_post_notification.delay(post_id)
    if post.featured_image:
        generate_post_thumbnail.delay(post_id)
    
    return {"message": "Post published successfully"}
```

### Task Scheduling

Schedule periodic tasks:

```python
# celery_beat.py
from celery.schedules import crontab
from celery_app import celery_app

celery_app.conf.beat_schedule = {
    # Run every day at 2 AM
    'cleanup-old-posts': {
        'task': 'apps.blog.tasks.cleanup_old_posts',
        'schedule': crontab(hour=2, minute=0),
    },
    
    # Run every hour
    'update-post-stats': {
        'task': 'apps.blog.tasks.update_post_statistics',
        'schedule': crontab(minute=0),
    },
    
    # Run every Monday at 9 AM
    'weekly-newsletter': {
        'task': 'apps.blog.tasks.send_weekly_newsletter',
        'schedule': crontab(hour=9, minute=0, day_of_week=1),
    },
}

celery_app.conf.timezone = 'UTC'

# Start Celery worker and beat
# celery -A celery_app worker --loglevel=info
# celery -A celery_app beat --loglevel=info
```

### Simple Background Tasks

For simpler use cases, use built-in background tasks:

```python
from fabiplus.core.tasks import background_task
import asyncio

@background_task
async def process_uploaded_file(file_path: str):
    """Process uploaded file in background"""
    # Simulate file processing
    await asyncio.sleep(5)
    
    # Process file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Save processed content
    processed_path = file_path.replace('.txt', '_processed.txt')
    with open(processed_path, 'w') as f:
        f.write(content.upper())
    
    return processed_path

# Usage in views
@router.post("/upload")
async def upload_file(file: UploadFile):
    # Save file
    file_path = f"uploads/{file.filename}"
    with open(file_path, 'wb') as f:
        f.write(await file.read())
    
    # Process in background
    task = process_uploaded_file(file_path)
    
    return {
        "message": "File uploaded successfully",
        "task_id": task.id,
        "status": "processing"
    }

@router.get("/tasks/{task_id}")
async def get_task_status(task_id: str):
    """Get background task status"""
    from fabiplus.core.tasks import get_task_status
    
    status = get_task_status(task_id)
    return {
        "task_id": task_id,
        "status": status.status,
        "result": status.result,
        "error": status.error
    }
```

## Middleware

FABI+ supports custom middleware for request/response processing.

### Built-in Middleware

FABI+ includes several built-in middleware components:

```python
# settings.py
class Settings(FABIPlusSettings):
    MIDDLEWARE = [
        "fabiplus.core.middleware.SecurityMiddleware",
        "fabiplus.core.middleware.CORSMiddleware", 
        "fabiplus.core.middleware.RateLimitMiddleware",
        "fabiplus.core.middleware.CacheMiddleware",
        "fabiplus.core.middleware.LoggingMiddleware",
        "fabiplus.core.middleware.ErrorHandlingMiddleware",
    ]
    
    # Middleware configuration
    SECURITY_MIDDLEWARE_CONFIG = {
        "force_https": True,
        "hsts_seconds": 31536000,
        "content_type_nosniff": True,
        "xss_protection": True,
    }
    
    RATE_LIMIT_CONFIG = {
        "default": "100/minute",
        "login": "5/minute",
        "api": "1000/hour",
    }
```

### Custom Middleware

Create custom middleware for specific needs:

```python
# middleware/custom.py
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import time
import logging

logger = logging.getLogger(__name__)

class RequestTimingMiddleware:
    """Middleware to track request processing time"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        start_time = time.time()
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                process_time = time.time() - start_time
                message["headers"].append([
                    b"x-process-time",
                    str(process_time).encode()
                ])
                
                # Log slow requests
                if process_time > 1.0:
                    logger.warning(
                        f"Slow request: {scope['path']} took {process_time:.2f}s"
                    )
            
            await send(message)
        
        await self.app(scope, receive, send_wrapper)

class RequestLoggingMiddleware:
    """Middleware to log all requests"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # Log request
        logger.info(
            f"{request.method} {request.url.path} "
            f"from {request.client.host}"
        )
        
        await self.app(scope, receive, send)

class APIKeyMiddleware:
    """Middleware to validate API keys"""
    
    def __init__(self, app):
        self.app = app
        self.protected_paths = ["/api/v1/admin/", "/api/v1/internal/"]
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        request = Request(scope, receive)
        
        # Check if path requires API key
        if any(request.url.path.startswith(path) for path in self.protected_paths):
            api_key = request.headers.get("X-API-Key")
            
            if not api_key or not self.validate_api_key(api_key):
                response = JSONResponse(
                    status_code=401,
                    content={"error": "Invalid or missing API key"}
                )
                await response(scope, receive, send)
                return
        
        await self.app(scope, receive, send)
    
    def validate_api_key(self, api_key: str) -> bool:
        """Validate API key against database or cache"""
        from fabiplus.core.models import APIKey
        return APIKey.filter(key=api_key, is_active=True).exists()

# Register middleware
from fastapi import FastAPI

app = FastAPI()

app.add_middleware(RequestTimingMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(APIKeyMiddleware)
```
