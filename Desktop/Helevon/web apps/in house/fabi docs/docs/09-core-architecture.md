# Core Architecture

This guide covers FABI+ framework internals, extension development, custom commands, and advanced architectural concepts.

## Table of Contents

- [Framework Architecture](#framework-architecture)
- [Core Components](#core-components)
- [Extension Development](#extension-development)
- [Custom CLI Commands](#custom-cli-commands)
- [Plugin System](#plugin-system)
- [Framework Internals](#framework-internals)
- [Testing Framework](#testing-framework)
- [Performance Considerations](#performance-considerations)

## Framework Architecture

FABI+ follows a modular, layered architecture designed for scalability and maintainability.

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │    Views    │  │   Admin     │  │    CLI Commands     │ │
│  │             │  │ Interface   │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Service Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │ Authentication│ │   Caching   │  │  Background Tasks   │ │
│  │   & Permissions│ │   System    │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                      Core Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Models    │  │  Database   │  │     Middleware      │ │
│  │   & ORM     │  │  Manager    │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │  FastAPI    │  │  Database   │  │    File System      │ │
│  │  Framework  │  │ (PostgreSQL,│  │   & Media Storage   │ │
│  │             │  │  SQLite)    │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Design Principles

1. **Separation of Concerns**: Each layer has distinct responsibilities
2. **Dependency Injection**: Components are loosely coupled through DI
3. **Configuration-Driven**: Behavior controlled through settings
4. **Plugin Architecture**: Extensible through plugins and hooks
5. **Type Safety**: Comprehensive type hints throughout the codebase

## Core Components

### Application Factory

The application factory pattern creates and configures the FastAPI application:

```python
# fabiplus/core/application.py
from fastapi import FastAPI
from fabiplus.conf import settings
from fabiplus.core.database import init_database
from fabiplus.core.middleware import setup_middleware
from fabiplus.core.routing import setup_routes
from fabiplus.admin import setup_admin

class FABIPlusApplication:
    """Main application factory"""
    
    def __init__(self, settings_module: str = None):
        self.settings = settings
        self.app = None
        self._plugins = []
    
    def create_app(self) -> FastAPI:
        """Create and configure FastAPI application"""
        self.app = FastAPI(
            title=self.settings.PROJECT_NAME,
            version=self.settings.VERSION,
            description=self.settings.DESCRIPTION,
            docs_url="/docs" if self.settings.ENABLE_DOCS else None,
            redoc_url="/redoc" if self.settings.ENABLE_DOCS else None,
        )
        
        # Setup core components
        self._setup_database()
        self._setup_middleware()
        self._setup_routing()
        self._setup_admin()
        self._setup_plugins()
        
        return self.app
    
    def _setup_database(self):
        """Initialize database connection"""
        init_database(self.app, self.settings)
    
    def _setup_middleware(self):
        """Setup middleware stack"""
        setup_middleware(self.app, self.settings)
    
    def _setup_routing(self):
        """Setup application routes"""
        setup_routes(self.app, self.settings)
    
    def _setup_admin(self):
        """Setup admin interface"""
        if self.settings.ADMIN_ENABLED:
            setup_admin(self.app, self.settings)
    
    def _setup_plugins(self):
        """Load and initialize plugins"""
        for plugin_class in self._discover_plugins():
            plugin = plugin_class(self.app, self.settings)
            plugin.initialize()
            self._plugins.append(plugin)
    
    def _discover_plugins(self):
        """Discover available plugins"""
        import importlib
        import pkgutil
        
        plugins = []
        for finder, name, ispkg in pkgutil.iter_modules(['plugins']):
            try:
                module = importlib.import_module(f'plugins.{name}')
                if hasattr(module, 'Plugin'):
                    plugins.append(module.Plugin)
            except ImportError:
                continue
        
        return plugins

# Usage
def create_app():
    factory = FABIPlusApplication()
    return factory.create_app()
```

### Database Manager

The database manager handles ORM-agnostic database operations:

```python
# fabiplus/core/database.py
from typing import Any, Dict, Optional, Type
from contextlib import contextmanager
from abc import ABC, abstractmethod

class DatabaseBackend(ABC):
    """Abstract database backend"""
    
    @abstractmethod
    def initialize(self, app: FastAPI, settings: Any):
        """Initialize database connection"""
        pass
    
    @abstractmethod
    def get_session(self):
        """Get database session"""
        pass
    
    @abstractmethod
    def create_tables(self):
        """Create database tables"""
        pass
    
    @abstractmethod
    def drop_tables(self):
        """Drop database tables"""
        pass

class SQLModelBackend(DatabaseBackend):
    """SQLModel database backend"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
    
    def initialize(self, app: FastAPI, settings: Any):
        from sqlmodel import create_engine, Session
        
        self.engine = create_engine(
            settings.DATABASE_URL,
            echo=settings.DATABASE_ECHO,
            **settings.SQLALCHEMY_ENGINE_OPTIONS
        )
        
        self.session_factory = lambda: Session(self.engine)
        
        # Add startup/shutdown events
        @app.on_event("startup")
        async def startup():
            self.create_tables()
        
        @app.on_event("shutdown")
        async def shutdown():
            self.engine.dispose()
    
    @contextmanager
    def get_session(self):
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def create_tables(self):
        from sqlmodel import SQLModel
        SQLModel.metadata.create_all(self.engine)
    
    def drop_tables(self):
        from sqlmodel import SQLModel
        SQLModel.metadata.drop_all(self.engine)

class DatabaseManager:
    """Database manager with multiple backend support"""
    
    def __init__(self):
        self._backends = {
            'sqlmodel': SQLModelBackend,
            'sqlalchemy': SQLAlchemyBackend,
            'tortoise': TortoiseBackend,
        }
        self._current_backend = None
    
    def initialize(self, app: FastAPI, settings: Any):
        """Initialize database with specified backend"""
        backend_name = settings.ORM_BACKEND.lower()
        
        if backend_name not in self._backends:
            raise ValueError(f"Unsupported ORM backend: {backend_name}")
        
        backend_class = self._backends[backend_name]
        self._current_backend = backend_class()
        self._current_backend.initialize(app, settings)
    
    def get_session(self):
        """Get database session from current backend"""
        if not self._current_backend:
            raise RuntimeError("Database not initialized")
        
        return self._current_backend.get_session()
    
    def register_backend(self, name: str, backend_class: Type[DatabaseBackend]):
        """Register custom database backend"""
        self._backends[name] = backend_class

# Global database manager instance
db_manager = DatabaseManager()

def init_database(app: FastAPI, settings: Any):
    """Initialize database"""
    db_manager.initialize(app, settings)

def get_session():
    """Get database session"""
    return db_manager.get_session()
```

### Configuration System

The configuration system provides flexible, environment-aware settings:

```python
# fabiplus/conf/settings.py
from pydantic import BaseSettings, Field
from typing import List, Dict, Any, Optional
import os

class FABIPlusSettings(BaseSettings):
    """Base settings class for FABI+ applications"""
    
    # Project information
    PROJECT_NAME: str = Field(default="FABI+ API", env="PROJECT_NAME")
    VERSION: str = Field(default="1.0.0", env="VERSION")
    DESCRIPTION: str = Field(default="API built with FABI+", env="DESCRIPTION")
    
    # Environment
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # Database
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_ECHO: bool = Field(default=False, env="DATABASE_ECHO")
    ORM_BACKEND: str = Field(default="sqlmodel", env="ORM_BACKEND")
    
    # Security
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ALLOWED_HOSTS: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # API Documentation
    ENABLE_DOCS: bool = Field(default=True, env="ENABLE_DOCS")
    DOCS_URL: str = Field(default="/docs", env="DOCS_URL")
    REDOC_URL: str = Field(default="/redoc", env="REDOC_URL")
    
    # Admin Interface
    ADMIN_ENABLED: bool = Field(default=True, env="ADMIN_ENABLED")
    ADMIN_PREFIX: str = Field(default="/admin", env="ADMIN_PREFIX")
    
    # Caching
    CACHE_BACKEND: str = Field(default="memory", env="CACHE_BACKEND")
    CACHE_TTL: int = Field(default=300, env="CACHE_TTL")
    REDIS_URL: Optional[str] = Field(default=None, env="REDIS_URL")
    
    # Middleware
    MIDDLEWARE: List[str] = Field(default=[
        "fabiplus.core.middleware.SecurityMiddleware",
        "fabiplus.core.middleware.CORSMiddleware",
        "fabiplus.core.middleware.ErrorHandlingMiddleware",
    ])
    
    # File uploads
    MEDIA_ROOT: str = Field(default="media", env="MEDIA_ROOT")
    MEDIA_URL: str = Field(default="/media/", env="MEDIA_URL")
    MAX_UPLOAD_SIZE: int = Field(default=10485760, env="MAX_UPLOAD_SIZE")  # 10MB
    
    # Email
    EMAIL_BACKEND: str = Field(default="console", env="EMAIL_BACKEND")
    EMAIL_HOST: Optional[str] = Field(default=None, env="EMAIL_HOST")
    EMAIL_PORT: int = Field(default=587, env="EMAIL_PORT")
    EMAIL_USE_TLS: bool = Field(default=True, env="EMAIL_USE_TLS")
    EMAIL_HOST_USER: Optional[str] = Field(default=None, env="EMAIL_HOST_USER")
    EMAIL_HOST_PASSWORD: Optional[str] = Field(default=None, env="EMAIL_HOST_PASSWORD")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        import logging
        
        logging.basicConfig(
            level=getattr(logging, self.LOG_LEVEL),
            format=self.LOG_FORMAT
        )
    
    @property
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development"""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing"""
        return self.ENVIRONMENT.lower() == "testing"

# Settings registry for different environments
class DevelopmentSettings(FABIPlusSettings):
    """Development environment settings"""
    DEBUG: bool = True
    DATABASE_ECHO: bool = True
    CACHE_BACKEND: str = "memory"

class ProductionSettings(FABIPlusSettings):
    """Production environment settings"""
    DEBUG: bool = False
    DATABASE_ECHO: bool = False
    CACHE_BACKEND: str = "redis"
    ENABLE_DOCS: bool = False

class TestingSettings(FABIPlusSettings):
    """Testing environment settings"""
    DEBUG: bool = True
    DATABASE_URL: str = "sqlite:///test.db"
    CACHE_BACKEND: str = "memory"

def get_settings() -> FABIPlusSettings:
    """Get settings based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    settings_map = {
        "development": DevelopmentSettings,
        "production": ProductionSettings,
        "testing": TestingSettings,
    }
    
    settings_class = settings_map.get(env, DevelopmentSettings)
    return settings_class()

# Global settings instance
settings = get_settings()
```

### Event System

The event system provides hooks for extending framework behavior:

```python
# fabiplus/core/events.py
from typing import Any, Callable, Dict, List
from dataclasses import dataclass
import asyncio

@dataclass
class Event:
    """Base event class"""
    name: str
    data: Dict[str, Any]
    sender: Any = None

class EventManager:
    """Event manager for handling application events"""
    
    def __init__(self):
        self._listeners: Dict[str, List[Callable]] = {}
        self._middleware: List[Callable] = []
    
    def listen(self, event_name: str, callback: Callable):
        """Register event listener"""
        if event_name not in self._listeners:
            self._listeners[event_name] = []
        
        self._listeners[event_name].append(callback)
    
    def remove_listener(self, event_name: str, callback: Callable):
        """Remove event listener"""
        if event_name in self._listeners:
            try:
                self._listeners[event_name].remove(callback)
            except ValueError:
                pass
    
    def add_middleware(self, middleware: Callable):
        """Add event middleware"""
        self._middleware.append(middleware)
    
    async def emit(self, event_name: str, data: Dict[str, Any] = None, sender: Any = None):
        """Emit event to all listeners"""
        event = Event(name=event_name, data=data or {}, sender=sender)
        
        # Apply middleware
        for middleware in self._middleware:
            event = await self._call_handler(middleware, event)
            if event is None:
                return  # Middleware stopped event propagation
        
        # Call listeners
        if event_name in self._listeners:
            tasks = []
            for listener in self._listeners[event_name]:
                tasks.append(self._call_handler(listener, event))
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _call_handler(self, handler: Callable, event: Event):
        """Call event handler (sync or async)"""
        if asyncio.iscoroutinefunction(handler):
            return await handler(event)
        else:
            return handler(event)

# Global event manager
events = EventManager()

# Decorators for easy event handling
def on(event_name: str):
    """Decorator to register event listener"""
    def decorator(func):
        events.listen(event_name, func)
        return func
    return decorator

def middleware(func):
    """Decorator to register event middleware"""
    events.add_middleware(func)
    return func

# Built-in events
@on("app.startup")
async def on_app_startup(event: Event):
    """Handle application startup"""
    print(f"Application {event.data.get('app_name')} starting up...")

@on("model.created")
async def on_model_created(event: Event):
    """Handle model creation"""
    model = event.data.get('model')
    print(f"Model {model.__class__.__name__} created with ID {model.id}")

@on("user.login")
async def on_user_login(event: Event):
    """Handle user login"""
    user = event.data.get('user')
    print(f"User {user.username} logged in")

# Usage in application code
async def create_blog_post(post_data: dict):
    """Create blog post and emit event"""
    post = BlogPost(**post_data)
    post.save()
    
    # Emit event
    await events.emit("model.created", {"model": post})
    await events.emit("blog.post_created", {"post": post})
    
    return post
```

## Extension Development

### Creating Extensions

Extensions allow you to add functionality to FABI+ projects:

```python
# extensions/blog_analytics/extension.py
from fabiplus.core.extensions import Extension
from fabiplus.core.events import on
from fastapi import APIRouter

class BlogAnalyticsExtension(Extension):
    """Blog analytics extension"""
    
    name = "blog_analytics"
    version = "1.0.0"
    description = "Analytics for blog posts"
    
    def __init__(self, app, settings):
        super().__init__(app, settings)
        self.router = APIRouter(prefix="/analytics", tags=["analytics"])
    
    def initialize(self):
        """Initialize extension"""
        self._setup_routes()
        self._setup_event_listeners()
        self._setup_database()
        
        # Register router
        self.app.include_router(self.router)
    
    def _setup_routes(self):
        """Setup analytics routes"""
        
        @self.router.get("/posts/views")
        async def get_post_views():
            """Get post view statistics"""
            from .models import PostView
            
            views = await PostView.aggregate_views()
            return {"views": views}
        
        @self.router.get("/posts/popular")
        async def get_popular_posts():
            """Get popular posts"""
            from .models import PostView
            from apps.blog.models import BlogPost
            
            popular_post_ids = await PostView.get_popular_posts(limit=10)
            posts = await BlogPost.filter(id__in=popular_post_ids)
            
            return {"posts": posts}
    
    def _setup_event_listeners(self):
        """Setup event listeners"""
        
        @on("blog.post_viewed")
        async def track_post_view(event):
            """Track post view"""
            from .models import PostView
            
            post_id = event.data.get("post_id")
            user_id = event.data.get("user_id")
            ip_address = event.data.get("ip_address")
            
            await PostView.create(
                post_id=post_id,
                user_id=user_id,
                ip_address=ip_address
            )
    
    def _setup_database(self):
        """Setup database models"""
        from .models import PostView
        
        # Register models with ORM
        self.register_model(PostView)

# Extension models
# extensions/blog_analytics/models.py
from fabiplus.core.models import BaseModel
from sqlmodel import Field, select, func
from typing import Optional
from datetime import datetime

class PostView(BaseModel, table=True):
    __tablename__ = "blog_post_views"
    
    post_id: int = Field(foreign_key="blog_posts.id")
    user_id: Optional[int] = Field(default=None, foreign_key="users.id")
    ip_address: str = Field(max_length=45)
    user_agent: Optional[str] = Field(default=None, max_length=500)
    viewed_at: datetime = Field(default_factory=datetime.now)
    
    @classmethod
    async def aggregate_views(cls):
        """Get aggregated view statistics"""
        from fabiplus.core.database import get_session
        
        with get_session() as session:
            result = session.exec(
                select(
                    cls.post_id,
                    func.count(cls.id).label('view_count')
                ).group_by(cls.post_id)
            ).all()
            
            return [{"post_id": r.post_id, "views": r.view_count} for r in result]
    
    @classmethod
    async def get_popular_posts(cls, limit: int = 10):
        """Get most popular post IDs"""
        from fabiplus.core.database import get_session
        
        with get_session() as session:
            result = session.exec(
                select(cls.post_id)
                .group_by(cls.post_id)
                .order_by(func.count(cls.id).desc())
                .limit(limit)
            ).all()
            
            return [r.post_id for r in result]

# Extension configuration
# extensions/blog_analytics/config.py
from pydantic import BaseSettings

class BlogAnalyticsSettings(BaseSettings):
    """Blog analytics extension settings"""
    
    ANALYTICS_ENABLED: bool = True
    TRACK_ANONYMOUS_VIEWS: bool = True
    VIEW_RETENTION_DAYS: int = 365
    
    class Config:
        env_prefix = "BLOG_ANALYTICS_"
```

### Extension Registry

The extension registry manages installed extensions:

```python
# fabiplus/core/extensions.py
from typing import Dict, List, Type
from abc import ABC, abstractmethod
import importlib
import pkgutil

class Extension(ABC):
    """Base extension class"""
    
    name: str = None
    version: str = "1.0.0"
    description: str = ""
    dependencies: List[str] = []
    
    def __init__(self, app, settings):
        self.app = app
        self.settings = settings
        self._models = []
    
    @abstractmethod
    def initialize(self):
        """Initialize extension"""
        pass
    
    def register_model(self, model_class):
        """Register model with extension"""
        self._models.append(model_class)
    
    def get_models(self):
        """Get registered models"""
        return self._models

class ExtensionRegistry:
    """Registry for managing extensions"""
    
    def __init__(self):
        self._extensions: Dict[str, Extension] = {}
        self._extension_classes: Dict[str, Type[Extension]] = {}
    
    def discover_extensions(self, package_name: str = "extensions"):
        """Discover available extensions"""
        try:
            package = importlib.import_module(package_name)
            for finder, name, ispkg in pkgutil.iter_modules(package.__path__):
                if ispkg:
                    self._load_extension_module(f"{package_name}.{name}")
        except ImportError:
            pass  # No extensions package
    
    def _load_extension_module(self, module_name: str):
        """Load extension module"""
        try:
            module = importlib.import_module(f"{module_name}.extension")
            
            # Find extension classes
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    issubclass(attr, Extension) and 
                    attr != Extension):
                    
                    self._extension_classes[attr.name] = attr
        except ImportError:
            pass
    
    def register_extension(self, extension_class: Type[Extension]):
        """Register extension class"""
        self._extension_classes[extension_class.name] = extension_class
    
    def install_extension(self, name: str, app, settings) -> Extension:
        """Install and initialize extension"""
        if name not in self._extension_classes:
            raise ValueError(f"Extension '{name}' not found")
        
        extension_class = self._extension_classes[name]
        extension = extension_class(app, settings)
        
        # Check dependencies
        self._check_dependencies(extension)
        
        # Initialize extension
        extension.initialize()
        
        self._extensions[name] = extension
        return extension
    
    def _check_dependencies(self, extension: Extension):
        """Check extension dependencies"""
        for dep in extension.dependencies:
            if dep not in self._extensions:
                raise ValueError(
                    f"Extension '{extension.name}' requires '{dep}' "
                    f"but it's not installed"
                )
    
    def get_extension(self, name: str) -> Extension:
        """Get installed extension"""
        return self._extensions.get(name)
    
    def list_extensions(self) -> List[str]:
        """List installed extensions"""
        return list(self._extensions.keys())
    
    def list_available_extensions(self) -> List[str]:
        """List available extensions"""
        return list(self._extension_classes.keys())

# Global extension registry
extension_registry = ExtensionRegistry()
```

---

**Previous**: [← Advanced Features](08-advanced-features.md) | **Next**: [Contributing →](10-contributing.md)
