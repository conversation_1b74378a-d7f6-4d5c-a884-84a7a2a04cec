# Installation & Setup

This guide provides detailed instructions for installing and configuring FABI+ for different environments and use cases.

## System Requirements

### Minimum Requirements
- **Python**: 3.10 or higher
- **Memory**: 512MB RAM
- **Storage**: 100MB free space
- **OS**: Linux, macOS, or Windows

### Recommended Requirements
- **Python**: 3.11 or higher
- **Memory**: 2GB RAM or more
- **Storage**: 1GB free space
- **Database**: PostgreSQL 12+ or MySQL 8+
- **Cache**: Redis 6+ (optional but recommended)

## Installation Methods

### Method 1: Using pip (Recommended)

Install FABI+ directly from PyPI:

```bash
pip install fabiplus
```

Verify the installation:

```bash
fabiplus version
fabiplus info
```

### Method 2: Development Installation

For contributing to FABI+ or using the latest development version:

```bash
# Clone the repository
git clone https://github.com/helevon/fabiplus.git
cd fabiplus

# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Activate the virtual environment
poetry shell

# Verify installation
fabiplus version
```

### Method 3: Using Docker

Run FABI+ in a Docker container:

```bash
# Pull the official image
docker pull fabiplus/fabiplus:latest

# Run a container
docker run -p 8000:8000 fabiplus/fabiplus:latest
```

## Poetry Setup and Virtual Environments

FABI+ uses Poetry for dependency management and virtual environments.

### Installing Poetry

**On macOS/Linux:**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

**On Windows (PowerShell):**
```powershell
(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -
```

**Alternative (using pip):**
```bash
pip install poetry
```

### Configuring Poetry

Configure Poetry for optimal FABI+ development:

```bash
# Configure Poetry to create virtual environments in project directory
poetry config virtualenvs.in-project true

# Configure Poetry to use Python 3.11 (if available)
poetry config virtualenvs.prefer-active-python true

# Show current configuration
poetry config --list
```

### Working with Virtual Environments

```bash
# Create a new project with Poetry
poetry new myproject
cd myproject

# Add FABI+ as a dependency
poetry add fabiplus

# Install dependencies
poetry install

# Activate the virtual environment
poetry shell

# Run commands within the environment
poetry run fabiplus --help

# Deactivate the environment
exit
```

## Database Configuration

FABI+ supports multiple database backends through its ORM abstraction layer.

### SQLite (Default)

SQLite is used by default for development and requires no additional setup:

```python
# In your project's settings.py or .env file
DATABASE_URL = "sqlite:///./fabiplus.db"
```

### PostgreSQL

Install the PostgreSQL adapter:

```bash
# Using Poetry
poetry add psycopg2-binary

# Or using pip
pip install psycopg2-binary
```

Configure the database URL:

```python
# In your .env file
DATABASE_URL = "postgresql://username:password@localhost:5432/database_name"
```

**Docker PostgreSQL Setup:**
```bash
# Run PostgreSQL in Docker
docker run --name fabiplus-postgres \
  -e POSTGRES_DB=fabiplus \
  -e POSTGRES_USER=fabiplus \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  -d postgres:15
```

### MySQL

Install the MySQL adapter:

```bash
# Using Poetry
poetry add pymysql

# Or using pip
pip install pymysql
```

Configure the database URL:

```python
# In your .env file
DATABASE_URL = "mysql+pymysql://username:password@localhost:3306/database_name"
```

**Docker MySQL Setup:**
```bash
# Run MySQL in Docker
docker run --name fabiplus-mysql \
  -e MYSQL_DATABASE=fabiplus \
  -e MYSQL_USER=fabiplus \
  -e MYSQL_PASSWORD=password \
  -e MYSQL_ROOT_PASSWORD=rootpassword \
  -p 3306:3306 \
  -d mysql:8.0
```

### Database Connection Testing

Test your database connection:

```bash
# Check database info
fabiplus db info

# Test connection
fabiplus db check

# Create tables
fabiplus db create-tables
```

## Environment Variables Configuration

FABI+ uses environment variables for configuration. Create a `.env` file in your project root:

### Basic Configuration

```bash
# .env file
# Development settings
DEBUG=true
ENVIRONMENT=development

# Server settings
HOST=127.0.0.1
PORT=8000
RELOAD=true

# Database
DATABASE_URL=sqlite:///./fabiplus.db
DATABASE_ECHO=false

# Security
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# API settings
API_PREFIX=/api
API_VERSION=v1
DOCS_URL=/docs
REDOC_URL=/redoc

# Admin settings
ADMIN_ENABLED=true
ADMIN_PREFIX=/admin
```

### Advanced Configuration

```bash
# Security features
SECURITY_ENABLED=true
RATE_LIMITING_ENABLED=true
RATE_LIMIT_PER_MINUTE=60

# CORS settings
CORS_ENABLED=true
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_CREDENTIALS=true
CORS_METHODS=["GET", "POST", "PUT", "DELETE"]
CORS_HEADERS=["*"]

# Caching
CACHE_BACKEND=memory
CACHE_TTL=300
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/fabiplus.log
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# File uploads
MAX_UPLOAD_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx

# Email (optional)
EMAIL_BACKEND=console
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_USE_TLS=true
```

### Environment-Specific Configuration

Create different `.env` files for different environments:

**Development (`.env.development`):**
```bash
DEBUG=true
ENVIRONMENT=development
DATABASE_URL=sqlite:///./dev.db
LOG_LEVEL=DEBUG
RELOAD=true
```

**Testing (`.env.testing`):**
```bash
DEBUG=false
ENVIRONMENT=testing
DATABASE_URL=sqlite:///./test.db
LOG_LEVEL=WARNING
CACHE_BACKEND=memory
```

**Production (`.env.production`):**
```bash
DEBUG=false
ENVIRONMENT=production
DATABASE_URL=***********************************/fabiplus
LOG_LEVEL=INFO
CACHE_BACKEND=redis
REDIS_URL=redis://prod-redis:6379/0
RELOAD=false
```

## Redis Setup (Optional)

Redis provides high-performance caching and session storage.

### Installing Redis

**On macOS:**
```bash
brew install redis
brew services start redis
```

**On Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**Using Docker:**
```bash
docker run --name fabiplus-redis \
  -p 6379:6379 \
  -d redis:7-alpine
```

### Configuring Redis

Install the Redis Python client:

```bash
poetry add redis hiredis
```

Update your `.env` file:

```bash
CACHE_BACKEND=redis
REDIS_URL=redis://localhost:6379/0
```

Test Redis connection:

```bash
fabiplus cache info
```

## Project Structure Setup

When you create a new FABI+ project, it follows this structure:

```
myproject/
├── myproject/              # Main project package
│   ├── __init__.py
│   ├── settings.py         # Project settings
│   ├── urls.py            # URL configuration
│   └── wsgi.py            # WSGI application
├── apps/                  # Project apps
│   └── core/              # Core app (created by default)
│       ├── __init__.py
│       ├── models.py
│       ├── views.py
│       ├── admin.py
│       ├── serializers.py
│       ├── urls.py
│       └── tests.py
├── static/                # Static files
├── media/                 # Media uploads
├── logs/                  # Log files
├── migrations/            # Database migrations
├── manage.py              # Management script
├── pyproject.toml         # Poetry configuration
├── .env                   # Environment variables
├── .env.example           # Environment template
├── .gitignore            # Git ignore rules
├── README.md             # Project documentation
└── Dockerfile            # Docker configuration (optional)
```

## IDE Configuration

### VS Code

Create `.vscode/settings.json`:

```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

### PyCharm

1. Open your project in PyCharm
2. Go to **File → Settings → Project → Python Interpreter**
3. Click the gear icon and select **Add**
4. Choose **Poetry Environment** and select **Existing environment**
5. Point to your Poetry virtual environment

## Troubleshooting

### Common Installation Issues

**Poetry not found:**
```bash
# Add Poetry to PATH
export PATH="$HOME/.local/bin:$PATH"
# Add to your shell profile (.bashrc, .zshrc, etc.)
```

**Python version issues:**
```bash
# Install Python 3.11 using pyenv
pyenv install 3.11.0
pyenv global 3.11.0

# Or specify Python version for Poetry
poetry env use python3.11
```

**Database connection errors:**
```bash
# Check database service status
sudo systemctl status postgresql
sudo systemctl status mysql

# Test database connection
fabiplus db check
```

**Permission errors:**
```bash
# Fix Poetry permissions
sudo chown -R $USER:$USER ~/.cache/pypoetry
sudo chown -R $USER:$USER ~/.config/pypoetry
```

### Verification Steps

After installation, verify everything is working:

```bash
# Check FABI+ version
fabiplus version

# Check system info
fabiplus info

# Create a test project
fabiplus project startproject testproject
cd testproject

# Install dependencies
poetry install

# Check database
fabiplus db info

# Run server
fabiplus server run
```

## Next Steps

Now that FABI+ is installed and configured:

1. **[CLI Commands Reference](04-cli-commands.md)** - Learn all available commands
2. **[Project Management](05-project-management.md)** - Create and manage projects
3. **[API Development](06-api-development.md)** - Build your first API
4. **[Admin Interface](07-admin-interface.md)** - Set up the admin panel

---

**Previous**: [← Quick Start](02-quick-start.md) | **Next**: [CLI Commands Reference →](04-cli-commands.md)
