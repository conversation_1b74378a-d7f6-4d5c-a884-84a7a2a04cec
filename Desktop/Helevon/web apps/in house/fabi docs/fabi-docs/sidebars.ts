import type {SidebarsConfig} from '@docusaurus/plugin-content-docs';

// This runs in Node.js - Don't use client-side code here (browser APIs, JSX...)

/**
 * Fabi+ Documentation Sidebar Configuration
 * Organized by user journey and logical grouping for optimal developer experience
 */
const sidebars: SidebarsConfig = {
  docsSidebar: [
    // Getting Started Section
    {
      type: 'category',
      label: '🚀 Getting Started',
      collapsed: false,
      items: [
        'introduction',
        'quick-start',
        'installation-setup',
      ],
    },

    // More sections will be added as documentation is migrated
    // This is a minimal working sidebar for the initial setup
  ],
};

export default sidebars;
