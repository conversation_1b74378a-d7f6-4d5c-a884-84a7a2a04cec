---
sidebar_position: 5
title: Project Management
description: Creating, configuring, and managing FABI+ projects including structure, apps, migrations, and media system.
keywords: [fabi+, project management, project structure, apps, migrations, media, configuration]
---

import ORMTabs from '@site/src/components/ORMTabs';
import ORMCodeBlock from '@site/src/components/ORMCodeBlock';

# Project Management

This guide covers creating, configuring, and managing FABI+ projects, including project structure, app management, database migrations, and the media system.

## Project Creation

### Creating a New Project

Use the `startproject` command to create a new FABI+ project:

```bash
fabiplus project startproject myproject
```

This creates a complete project structure with:
- Project configuration files
- Poetry setup with dependencies
- Environment configuration
- Basic app structure
- Example models and views

### Project Creation Options

**ORM Backend Selection:**
```bash
# SQLModel (default) - Modern, type-safe ORM
fabiplus project startproject myapi --orm sqlmodel

# SQLAlchemy - Mature, feature-rich ORM
fabiplus project startproject myapi --orm sqlalchemy

# Tortoise ORM - Async-native ORM
fabiplus project startproject myapi --orm tortoise
```

**Authentication Backend:**
```bash
# OAuth2 (default) - Industry standard
fabiplus project startproject myapi --auth oauth2

# JWT - JSON Web Tokens
fabiplus project startproject myapi --auth jwt
```

**Docker Support:**
```bash
# Include Docker files
fabiplus project startproject myapi --docker
```

**Complete Example:**
```bash
fabiplus project startproject ecommerce \
  --orm sqlmodel \
  --auth jwt \
  --docker \
  --show-admin-routes
```

## Project Structure

A FABI+ project follows this structure:

```
myproject/
├── myproject/                 # Main project package
│   ├── __init__.py
│   ├── settings.py           # Project configuration
│   ├── urls.py              # URL routing
│   └── wsgi.py              # WSGI application
├── apps/                    # Project applications
│   └── core/                # Default core app
│       ├── __init__.py
│       ├── models.py        # Database models
│       ├── views.py         # API endpoints
│       ├── admin.py         # Admin configuration
│       ├── serializers.py   # Pydantic schemas
│       ├── urls.py          # App URL routing
│       └── tests.py         # Test cases
├── static/                  # Static files (CSS, JS, images)
├── media/                   # User-uploaded files
├── logs/                    # Application logs
├── migrations/              # Database migration files
├── manage.py               # Management script
├── pyproject.toml          # Poetry configuration
├── .env                    # Environment variables
├── .env.example            # Environment template
├── .gitignore             # Git ignore rules
├── README.md              # Project documentation
└── Dockerfile             # Docker configuration (optional)
```

### Key Files Explained

**`myproject/settings.py`** - Main configuration file:

<ORMCodeBlock
  language="python"
  code={`from fabiplus.conf import FABIPlusSettings
from typing import List

class Settings(FABIPlusSettings):
    """Project-specific settings"""
    
    PROJECT_NAME: str = "My FABI+ Project"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "A modern FastAPI application"
    
    # Database configuration
    DATABASE_URL: str = "sqlite:///./app.db"
    
    # Installed apps
    INSTALLED_APPS: List[str] = [
        "apps.core",
        "apps.users",
        "apps.blog",
    ]
    
    # Middleware
    MIDDLEWARE: List[str] = [
        "fabiplus.middleware.SecurityMiddleware",
        "fabiplus.middleware.CORSMiddleware",
        "fabiplus.middleware.ErrorHandlingMiddleware",
    ]
    
    # Admin configuration
    ADMIN_ENABLED: bool = True
    ADMIN_PREFIX: str = "/admin"

settings = Settings()`}
/>

**`manage.py`** - Management script:

<ORMCodeBlock
  language="python"
  code={`#!/usr/bin/env python3
"""Management script for FABI+ project"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set default settings module
os.environ.setdefault("FABIPLUS_SETTINGS_MODULE", "myproject.settings")

if __name__ == "__main__":
    from fabiplus.core.management import execute_from_command_line
    execute_from_command_line(sys.argv)`}
/>

## App Management

### Creating Apps

Apps are modular components that organize related functionality:

```bash
# Create a new app
fabiplus app startapp blog

# Create app with specific models
fabiplus app startapp ecommerce --models Product,Category,Order
```

### App Structure

Each app follows a consistent structure:

```
apps/blog/
├── __init__.py
├── models.py          # Database models
├── views.py           # API endpoints
├── admin.py           # Admin interface
├── serializers.py     # Pydantic schemas
├── urls.py            # URL routing
├── tests.py           # Test cases
├── migrations/        # App-specific migrations
└── fixtures/          # Test data
```

### Model Definition

Define your models using your chosen ORM:

<ORMTabs
  sqlmodel={`# apps/blog/models.py
from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime

class Category(SQLModel, table=True):
    __tablename__ = "blog_categories"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(max_length=100, unique=True)
    slug: str = Field(max_length=100, unique=True)
    description: Optional[str] = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.now)
    
    # Relationships
    posts: List["BlogPost"] = Relationship(back_populates="category")

class BlogPost(SQLModel, table=True):
    __tablename__ = "blog_posts"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    title: str = Field(max_length=200)
    slug: str = Field(max_length=200, unique=True)
    content: str
    excerpt: Optional[str] = Field(default=None, max_length=500)
    published: bool = Field(default=False)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    # Foreign keys
    category_id: Optional[int] = Field(default=None, foreign_key="blog_categories.id")
    author_id: int = Field(foreign_key="users.id")
    
    # Relationships
    category: Optional[Category] = Relationship(back_populates="posts")
    author: "User" = Relationship(back_populates="blog_posts")`}
  sqlalchemy={`# apps/blog/models.py
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class Category(Base):
    __tablename__ = "blog_categories"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False)
    slug = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.now)
    
    # Relationships
    posts = relationship("BlogPost", back_populates="category")

class BlogPost(Base):
    __tablename__ = "blog_posts"
    
    id = Column(Integer, primary_key=True)
    title = Column(String(200), nullable=False)
    slug = Column(String(200), unique=True, nullable=False)
    content = Column(Text, nullable=False)
    excerpt = Column(String(500))
    published = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Foreign keys
    category_id = Column(Integer, ForeignKey("blog_categories.id"))
    author_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    category = relationship("Category", back_populates="posts")
    author = relationship("User", back_populates="blog_posts")`}
  tortoise={`# apps/blog/models.py
from tortoise.models import Model
from tortoise import fields
from datetime import datetime

class Category(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=100, unique=True)
    slug = fields.CharField(max_length=100, unique=True)
    description = fields.TextField(null=True)
    created_at = fields.DatetimeField(default=datetime.now)
    
    # Relationships
    posts: fields.ReverseRelation["BlogPost"]
    
    class Meta:
        table = "blog_categories"

class BlogPost(Model):
    id = fields.IntField(pk=True)
    title = fields.CharField(max_length=200)
    slug = fields.CharField(max_length=200, unique=True)
    content = fields.TextField()
    excerpt = fields.CharField(max_length=500, null=True)
    published = fields.BooleanField(default=False)
    created_at = fields.DatetimeField(default=datetime.now)
    updated_at = fields.DatetimeField(auto_now=True)
    
    # Foreign keys
    category: fields.ForeignKeyNullableRelation[Category] = fields.ForeignKeyField(
        "models.Category", related_name="posts", null=True
    )
    author: fields.ForeignKeyRelation["User"] = fields.ForeignKeyField(
        "models.User", related_name="blog_posts"
    )
    
    class Meta:
        table = "blog_posts"`}
  title="Model Definition Examples"
/>

### Views and Serializers

Create API endpoints with proper serialization:

<ORMCodeBlock
  language="python"
  code={`# apps/blog/serializers.py
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class CategoryBase(BaseModel):
    name: str = Field(..., max_length=100)
    slug: str = Field(..., max_length=100)
    description: Optional[str] = None

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(CategoryBase):
    name: Optional[str] = None
    slug: Optional[str] = None

class CategoryResponse(CategoryBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class BlogPostBase(BaseModel):
    title: str = Field(..., max_length=200)
    slug: str = Field(..., max_length=200)
    content: str
    excerpt: Optional[str] = Field(None, max_length=500)
    published: bool = False
    category_id: Optional[int] = None

class BlogPostCreate(BlogPostBase):
    pass

class BlogPostUpdate(BlogPostBase):
    title: Optional[str] = None
    slug: Optional[str] = None
    content: Optional[str] = None

class BlogPostResponse(BlogPostBase):
    id: int
    created_at: datetime
    updated_at: datetime
    author_id: int
    category: Optional[CategoryResponse] = None
    
    class Config:
        from_attributes = True`}
/>

## Database Migrations

### Creating Migrations

Generate migrations when you modify models:

```bash
# Create migration for specific app
fabiplus db makemigrations blog

# Create migration for all apps
fabiplus db makemigrations

# Create migration with custom name
fabiplus db makemigrations blog --name add_blog_tags
```

### Applying Migrations

Apply migrations to update your database:

```bash
# Apply all pending migrations
fabiplus db migrate

# Apply migrations for specific app
fabiplus db migrate blog

# Apply specific migration
fabiplus db migrate blog 0002
```

### Migration Management

```bash
# Show migration status
fabiplus db showmigrations

# Show SQL for migration
fabiplus db sqlmigrate blog 0001

# Reverse migration
fabiplus db migrate blog 0001
```

## Media System

### File Upload Configuration

Configure file uploads in your settings:

<ORMCodeBlock
  language="python"
  code={`# myproject/settings.py
class Settings(FABIPlusSettings):
    # Media configuration
    MEDIA_ROOT: str = "media"
    MEDIA_URL: str = "/media/"
    MAX_UPLOAD_SIZE: int = 10485760  # 10MB
    
    # Allowed file types
    ALLOWED_UPLOAD_EXTENSIONS: List[str] = [
        ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"
    ]
    
    # Storage backend
    STORAGE_BACKEND: str = "local"  # or "s3", "gcs"
    
    # S3 configuration (if using S3)
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_STORAGE_BUCKET_NAME: Optional[str] = None
    AWS_S3_REGION_NAME: Optional[str] = "us-east-1"`}
/>

### File Upload Views

Handle file uploads in your views:

<ORMCodeBlock
  language="python"
  code={`# apps/blog/views.py
from fastapi import APIRouter, UploadFile, File, HTTPException
from fabiplus.core.files import save_uploaded_file
from .models import BlogPost
from .serializers import BlogPostResponse

router = APIRouter(prefix="/blog", tags=["blog"])

@router.post("/posts/{post_id}/upload-image")
async def upload_post_image(
    post_id: int,
    file: UploadFile = File(...)
):
    """Upload image for blog post"""
    
    # Validate file type
    if not file.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
        raise HTTPException(400, "Invalid file type")
    
    # Save file
    file_path = await save_uploaded_file(
        file, 
        upload_to="blog/images",
        max_size=5242880  # 5MB
    )
    
    # Update post with image path
    post = await BlogPost.get(id=post_id)
    post.featured_image = file_path
    await post.save()
    
    return {"message": "Image uploaded successfully", "path": file_path}`}
/>

## Next Steps

Now that you understand project management:

1. **[CLI Commands](./cli-commands)** - Master the comprehensive CLI toolkit
2. **[API Development](./api-development)** - Build robust APIs with advanced patterns
3. **[Authentication](./authentication)** - Implement secure user authentication
4. **[Admin Interface](./admin-interface)** - Set up the Django-style admin panel

:::tip Project Organization
Keep your apps focused on specific functionality. Create new apps when features become complex enough to warrant separation.
:::
