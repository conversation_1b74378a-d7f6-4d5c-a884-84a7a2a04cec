---
sidebar_position: 6
title: CLI Commands Reference
description: Comprehensive reference for all FABI+ CLI commands including project management, database operations, and development tools.
keywords: [fabi+, cli, commands, reference, project, database, server, development]
---

import ORMTabs from '@site/src/components/ORMTabs';
import OR<PERSON><PERSON><PERSON><PERSON> from '@site/src/components/ORMCodeBlock';

# CLI Commands Reference

FABI+ provides a comprehensive command-line interface with over 40 commands organized into logical groups. This reference covers all available commands with syntax, options, and examples.

## Global Commands

### `fabiplus version`
Show FABI+ version information.

```bash
fabiplus version
```

**Output:**
```
FABI+ Framework v0.1.0
```

### `fabiplus info`
Display comprehensive system and framework information.

```bash
fabiplus info
```

**Output:**
```
System Information:
• Python: 3.11.0
• Platform: Linux 5.15.0
• Architecture: x86_64

FABI+ Framework:
• Version: 0.1.0
• Location: /path/to/fabiplus

Available Commands:
• fabiplus project startproject - Create new project
• fabiplus app startapp - Create new app
• fabiplus server run - Run development server
• fabiplus db migrate - Run database migrations
• fabiplus user create - Create superuser
```

### `fabiplus quickstart`
Display a quick start guide with essential commands.

```bash
fabiplus quickstart
```

## Project Commands

### `fabiplus project startproject`
Create a new FABI+ project with complete structure.

**Syntax:**
```bash
fabiplus project startproject <name> [OPTIONS]
```

**Arguments:**
- `name` - Project name (must be a valid Python identifier)

**Options:**
- `--dir, -d <directory>` - Directory to create project in
- `--template, -t <template>` - Project template to use (default: "default")
- `--orm, -o <orm>` - ORM backend (sqlmodel, sqlalchemy, tortoise)
- `--auth, -a <auth>` - Authentication backend (oauth2, jwt)
- `--show-admin-routes` - Show admin routes in API docs
- `--force, -f` - Overwrite existing directory
- `--docker` - Include Docker files

**Examples:**
```bash
# Basic project creation
fabiplus project startproject myblog

# Create with specific ORM and Docker
fabiplus project startproject myapi --orm sqlmodel --docker

# Create in specific directory
fabiplus project startproject webapp --dir /path/to/projects

# Create with JWT authentication
fabiplus project startproject api --auth jwt --show-admin-routes
```

### `fabiplus project init`
Initialize FABI+ in an existing directory.

```bash
fabiplus project init [--force]
```

### `fabiplus project list-templates`
List available project templates.

```bash
fabiplus project list-templates
```

**Available Templates:**
- `default` - Standard project with all features
- `minimal` - Minimal project structure
- `api` - API-focused project
- `microservice` - Microservice template

## App Commands

### `fabiplus app startapp`
Create a new app within your project.

**Syntax:**
```bash
fabiplus app startapp <name> [OPTIONS]
```

**Options:**
- `--models, -m <models>` - Comma-separated list of model names
- `--admin` - Include admin configuration
- `--tests` - Include test files

**Examples:**
```bash
# Basic app creation
fabiplus app startapp blog

# Create app with models
fabiplus app startapp ecommerce --models Product,Category,Order

# Create app with admin and tests
fabiplus app startapp users --admin --tests
```

### `fabiplus app list`
List all apps in the current project.

```bash
fabiplus app list
```

## Server Commands

### `fabiplus server run`
Run the development server.

**Syntax:**
```bash
fabiplus server run [OPTIONS]
```

**Options:**
- `--host, -h <host>` - Host to bind to (default: 127.0.0.1)
- `--port, -p <port>` - Port to bind to (default: 8000)
- `--reload, -r` - Enable auto-reload on code changes
- `--workers, -w <count>` - Number of worker processes
- `--log-level <level>` - Log level (debug, info, warning, error)

**Examples:**
```bash
# Basic server start
fabiplus server run

# Run on specific host and port
fabiplus server run --host 0.0.0.0 --port 8080

# Run with auto-reload
fabiplus server run --reload

# Production mode with multiple workers
fabiplus server run --workers 4 --log-level warning
```

### `fabiplus server check`
Check project configuration and dependencies.

```bash
fabiplus server check [--deploy]
```

## Database Commands

### `fabiplus db makemigrations`
Create new database migrations.

**Syntax:**
```bash
fabiplus db makemigrations [app_name] [OPTIONS]
```

**Options:**
- `--name, -n <name>` - Custom migration name
- `--empty` - Create empty migration file
- `--dry-run` - Show what would be created without creating

**Examples:**

<ORMTabs
  sqlmodel={`# Create migrations for all apps
fabiplus db makemigrations

# Create migration for specific app
fabiplus db makemigrations blog

# Create migration with custom name
fabiplus db makemigrations blog --name add_blog_tags

# Create empty migration
fabiplus db makemigrations blog --empty --name custom_data_migration`}
  sqlalchemy={`# Create migrations for all apps
fabiplus db makemigrations

# Create migration for specific app
fabiplus db makemigrations blog

# Create migration with custom name
fabiplus db makemigrations blog --name add_blog_tags

# Create empty migration
fabiplus db makemigrations blog --empty --name custom_data_migration`}
  tortoise={`# Create migrations for all apps
fabiplus db makemigrations

# Create migration for specific app
fabiplus db makemigrations blog

# Create migration with custom name
fabiplus db makemigrations blog --name add_blog_tags

# Note: Tortoise ORM uses aerich for migrations
aerich init -t myproject.settings.TORTOISE_ORM
aerich init-db
aerich migrate`}
  title="Migration Commands by ORM"
/>

### `fabiplus db migrate`
Apply database migrations.

```bash
# Apply all pending migrations
fabiplus db migrate

# Apply migrations for specific app
fabiplus db migrate blog

# Apply up to specific migration
fabiplus db migrate blog 0002
```

### `fabiplus db showmigrations`
Show migration status.

```bash
fabiplus db showmigrations [app_name]
```

### `fabiplus db reset`
Reset database (drop and recreate all tables).

```bash
fabiplus db reset [--force]
```

## User Commands

### `fabiplus user create`
Create a superuser account.

```bash
fabiplus user create [--username <username>] [--email <email>]
```

### `fabiplus user list`
List all users.

```bash
fabiplus user list [--superusers-only]
```

### `fabiplus user changepassword`
Change user password.

```bash
fabiplus user changepassword <username>
```

## Cache Commands

### `fabiplus cache clear`
Clear application cache.

```bash
# Clear all cache
fabiplus cache clear

# Clear specific cache key
fabiplus cache clear --key user_sessions

# Clear cache by pattern
fabiplus cache clear --pattern "user_*"
```

### `fabiplus cache stats`
Show cache statistics.

```bash
fabiplus cache stats
```

## Development Commands

### `fabiplus dev shell`
Start an interactive Python shell with project context.

```bash
fabiplus dev shell
```

### `fabiplus dev test`
Run project tests.

**Syntax:**
```bash
fabiplus dev test [OPTIONS] [test_path]
```

**Options:**
- `--coverage` - Generate coverage report
- `--verbose, -v` - Verbose output
- `--failfast` - Stop on first failure

**Examples:**
```bash
# Run all tests
fabiplus dev test

# Run specific app tests
fabiplus dev test apps.blog.tests

# Run with coverage
fabiplus dev test --coverage

# Run specific test class
fabiplus dev test apps.blog.tests.TestBlogPost
```

### `fabiplus dev collectstatic`
Collect static files for production.

```bash
fabiplus dev collectstatic [--clear] [--dry-run]
```

### `fabiplus dev docs`
Generate API documentation.

```bash
fabiplus dev docs [--format openapi|redoc] [--output <file>]
```

## Custom Commands

You can create custom management commands for your project:

<ORMCodeBlock
  language="python"
  code={`# apps/blog/management/commands/import_posts.py
from fabiplus.core.management.base import BaseCommand
from apps.blog.models import BlogPost
import json

class Command(BaseCommand):
    """Import blog posts from JSON file"""
    
    help = "Import blog posts from JSON file"
    
    def add_arguments(self, parser):
        parser.add_argument('file', type=str, help='JSON file to import')
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be imported without importing'
        )
    
    def handle(self, *args, **options):
        file_path = options['file']
        dry_run = options['dry_run']
        
        with open(file_path, 'r') as f:
            posts_data = json.load(f)
        
        for post_data in posts_data:
            if dry_run:
                self.stdout.write(f"Would import: {post_data['title']}")
            else:
                post = BlogPost(**post_data)
                post.save()
                self.stdout.write(
                    self.style.SUCCESS(f"Imported: {post.title}")
                )
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully imported {len(posts_data)} posts")
            )`}
/>

**Usage:**
```bash
fabiplus blog import_posts posts.json
fabiplus blog import_posts posts.json --dry-run
```

## Command Help

Get help for any command:

```bash
# General help
fabiplus --help

# Command group help
fabiplus project --help

# Specific command help
fabiplus project startproject --help
```

## Environment Variables

Many commands respect environment variables for configuration:

<ORMCodeBlock
  language="bash"
  code={`# Database configuration
export DATABASE_URL="postgresql://user:pass@localhost/mydb"
export DATABASE_ECHO=true

# Server configuration
export HOST=0.0.0.0
export PORT=8080
export WORKERS=4

# Development settings
export DEBUG=true
export LOG_LEVEL=debug

# Cache configuration
export CACHE_BACKEND=redis
export REDIS_URL="redis://localhost:6379/0"`}
/>

## Command Aliases

Create aliases for frequently used commands:

<ORMCodeBlock
  language="bash"
  code={`# Add to ~/.bashrc or ~/.zshrc
alias fp="fabiplus"
alias fps="fabiplus server run"
alias fpm="fabiplus db migrate"
alias fpma="fabiplus db makemigrations"
alias fpt="fabiplus dev test"
alias fpu="fabiplus user create"`}
/>

## Next Steps

Now that you know the CLI commands:

1. **[API Development](./api-development)** - Build robust APIs with advanced patterns
2. **[Authentication](./authentication)** - Implement secure user authentication
3. **[Admin Interface](./admin-interface)** - Set up the Django-style admin panel
4. **[Advanced Features](./advanced-features)** - Explore caching, monitoring, and optimization

:::tip Command Completion
Enable shell completion for better CLI experience:
```bash
# For bash
fabiplus completion bash >> ~/.bashrc

# For zsh
fabiplus completion zsh >> ~/.zshrc
```
:::
