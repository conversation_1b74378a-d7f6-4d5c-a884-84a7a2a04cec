---
sidebar_position: 3
title: Installation & Setup
description: Detailed instructions for installing and configuring Fabi+ for different environments and use cases.
keywords: [fabi+, installation, setup, python, poetry, docker, configuration]
---

import ORMTabs from '@site/src/components/ORMTabs';

# Installation & Setup

This guide provides detailed instructions for installing and configuring FABI+ for different environments and use cases.

## System Requirements

### Minimum Requirements
- **Python**: 3.10 or higher
- **Memory**: 512MB RAM
- **Storage**: 100MB free space
- **OS**: Linux, macOS, or Windows

### Recommended Requirements
- **Python**: 3.11 or higher
- **Memory**: 2GB RAM or more
- **Storage**: 1GB free space
- **Database**: PostgreSQL 12+ or MySQL 8+
- **Cache**: Redis 6+ (optional but recommended)

## Installation Methods

### Method 1: Using pip (Recommended)

Install FABI+ directly from PyPI:

```bash
pip install fabiplus
```

Verify the installation:

```bash
fabiplus version
fabiplus info
```

### Method 2: Development Installation

For contributing to FABI+ or using the latest development version:

```bash
# Clone the repository
git clone https://github.com/helevon/fabiplus.git
cd fabiplus

# Install Poetry if not already installed
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Activate the virtual environment
poetry shell

# Verify installation
fabiplus version
```

### Method 3: Using Docker

Run FABI+ in a Docker container:

```bash
# Pull the official image
docker pull fabiplus/fabiplus:latest

# Run a container
docker run -p 8000:8000 fabiplus/fabiplus:latest
```

## Poetry Setup and Virtual Environments

FABI+ uses Poetry for dependency management and virtual environments.

### Installing Poetry

**On macOS/Linux:**
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

**On Windows (PowerShell):**
```powershell
(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -
```

### Creating a New Project

```bash
# Create a new FABI+ project
fabiplus project startproject myproject

# Navigate to the project directory
cd myproject

# Install dependencies
poetry install

# Activate the virtual environment
poetry shell
```

## Database Configuration

FABI+ supports multiple database backends. Configure your database in the `.env` file:

### SQLite (Default)
```env
DATABASE_URL=sqlite:///./app.db
```

### PostgreSQL
```env
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
```

### MySQL
```env
DATABASE_URL=mysql://username:password@localhost:3306/database_name
```

## ORM Configuration

Choose your preferred ORM and configure it in your project settings:

<ORMTabs
  sqlmodel={`# pyproject.toml
[tool.fabi]
orm = "sqlmodel"

# settings.py
from fabi.settings import Settings

class AppSettings(Settings):
    orm_backend: str = "sqlmodel"
    database_url: str = "sqlite:///./app.db"
    
    class Config:
        env_file = ".env"`}
  sqlalchemy={`# pyproject.toml
[tool.fabi]
orm = "sqlalchemy"

# settings.py
from fabi.settings import Settings

class AppSettings(Settings):
    orm_backend: str = "sqlalchemy"
    database_url: str = "sqlite:///./app.db"
    
    class Config:
        env_file = ".env"`}
  tortoise={`# pyproject.toml
[tool.fabi]
orm = "tortoise"

# settings.py
from fabi.settings import Settings

class AppSettings(Settings):
    orm_backend: str = "tortoise"
    database_url: str = "sqlite://app.db"
    
    class Config:
        env_file = ".env"`}
  title="ORM Configuration"
/>

## Environment Configuration

Create a `.env` file in your project root:

```env
# Basic Configuration
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DATABASE_URL=sqlite:///./app.db

# Security
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Cache (Optional)
REDIS_URL=redis://localhost:6379/0

# Email (Optional)
EMAIL_BACKEND=smtp
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Media Files
MEDIA_ROOT=media/
MEDIA_URL=/media/
MAX_UPLOAD_SIZE=10485760  # 10MB
```

## Development Server

Start the development server:

```bash
# Using the CLI
fabiplus server runserver

# With custom host and port
fabiplus server runserver --host 0.0.0.0 --port 8080

# With auto-reload (default in debug mode)
fabiplus server runserver --reload
```

## Production Setup

For production deployment, additional configuration is required:

### Environment Variables
```env
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
DATABASE_URL=postgresql://user:pass@localhost:5432/proddb
REDIS_URL=redis://localhost:6379/0
```

### Using Gunicorn
```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker
```

## Verification

Verify your installation by running:

```bash
# Check version
fabiplus version

# Check system info
fabiplus info

# Run health check
fabiplus health check

# Test database connection
fabiplus db check
```

## Next Steps

Now that you have FABI+ installed and configured:

1. [Create your first project](./quick-start) with the Quick Start guide
2. Explore the framework's core concepts and features
3. Build your first API endpoints
4. Set up authentication and security

:::tip Need Help?
If you encounter issues during installation, join our [Discord community](https://discord.gg/fabiplus) for help.
:::
