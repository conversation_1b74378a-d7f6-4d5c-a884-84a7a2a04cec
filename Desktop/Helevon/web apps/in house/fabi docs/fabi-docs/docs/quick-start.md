---
sidebar_position: 2
title: Quick Start
description: Get up and running with Fabi+ in under 5 minutes. Create your first API project with this step-by-step guide.
keywords: [fabi+, quick start, tutorial, api, python, fastapi, installation]
---

import ORMTabs from '@site/src/components/ORMTabs';

# Quick Start Guide

Get up and running with FABI+ in under 5 minutes! This guide will walk you through creating your first FABI+ project and building a simple blog API.

## Prerequisites

Before starting, ensure you have:
- Python 3.10 or higher installed
- Poetry package manager installed
- Basic knowledge of Python and APIs

### Installing Poetry

If you don't have Poetry installed:

```bash
# On macOS/Linux
curl -sSL https://install.python-poetry.org | python3 -

# On Windows (PowerShell)
(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -

# Add Poetry to your PATH (follow the instructions shown after installation)
```

## Step 1: Install FABI+

Install FABI+ framework using pip:

```bash
pip install fabiplus
```

Verify the installation:

```bash
fabiplus --help
```

You should see the FABI+ CLI help output with available commands.

## Step 2: Create Your First Project

Create a new FABI+ project called "myblog":

```bash
fabiplus project startproject myblog
```

This command creates a complete project structure with:
- Project configuration files
- Poetry setup with dependencies
- Environment configuration
- Basic app structure
- Example models and views

Navigate to your project directory:

```bash
cd myblog
```

## Step 3: Install Dependencies

Install the project dependencies using Poetry:

```bash
poetry install
```

This installs all required dependencies in a virtual environment.

## Step 4: Create Your First App

Create a blog app within your project:

```bash
fabiplus app startapp blog
```

This creates a new app in the `apps/blog/` directory with:
- `models.py` - Database models
- `views.py` - API endpoints
- `admin.py` - Admin interface configuration
- `serializers.py` - Pydantic schemas
- `urls.py` - URL routing
- `tests.py` - Test cases

## Step 5: Set Up the Database

Initialize the database and run migrations:

```bash
# Create migration files
fabiplus db makemigrations

# Apply migrations to create tables
fabiplus db migrate
```

## Step 6: Create Your First Model

Edit `apps/blog/models.py` to define a blog post model:

<ORMTabs
  sqlmodel={`from sqlmodel import SQLModel, Field
from typing import Optional
from datetime import datetime
import uuid

class BlogPost(SQLModel, table=True):
    __tablename__ = "blog_posts"
    
    id: uuid.UUID = Field(primary_key=True, default_factory=uuid.uuid4)
    title: str = Field(max_length=200)
    content: str
    author: str = Field(max_length=100)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    is_published: bool = Field(default=False)`}
  sqlalchemy={`from sqlalchemy import Column, String, Text, Boolean, DateTime, UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import uuid

Base = declarative_base()

class BlogPost(Base):
    __tablename__ = "blog_posts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(200), nullable=False)
    content = Column(Text, nullable=False)
    author = Column(String(100), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    is_published = Column(Boolean, default=False)`}
  tortoise={`from tortoise.models import Model
from tortoise import fields

class BlogPost(Model):
    id = fields.UUIDField(pk=True)
    title = fields.CharField(max_length=200)
    content = fields.TextField()
    author = fields.CharField(max_length=100)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    is_published = fields.BooleanField(default=False)
    
    class Meta:
        table = "blog_posts"`}
  title="Blog Post Model"
/>

## Step 7: Create API Endpoints

Edit `apps/blog/views.py` to create API endpoints:

```python
from fastapi import APIRouter, HTTPException, Depends
from typing import List
from .models import BlogPost
from .serializers import BlogPostCreate, BlogPostResponse
from fabi.database import get_session

router = APIRouter(prefix="/blog", tags=["Blog"])

@router.get("/posts", response_model=List[BlogPostResponse])
async def list_posts(session=Depends(get_session)):
    """Get all published blog posts"""
    posts = session.query(BlogPost).filter(BlogPost.is_published == True).all()
    return posts

@router.post("/posts", response_model=BlogPostResponse)
async def create_post(post: BlogPostCreate, session=Depends(get_session)):
    """Create a new blog post"""
    db_post = BlogPost(**post.dict())
    session.add(db_post)
    session.commit()
    session.refresh(db_post)
    return db_post

@router.get("/posts/{post_id}", response_model=BlogPostResponse)
async def get_post(post_id: str, session=Depends(get_session)):
    """Get a specific blog post"""
    post = session.query(BlogPost).filter(BlogPost.id == post_id).first()
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    return post
```

## Step 8: Run Your Application

Start the development server:

```bash
fabiplus server runserver
```

Your API is now running at `http://localhost:8000`!

## Step 9: Explore Your API

1. **API Documentation**: Visit `http://localhost:8000/docs` for interactive API docs
2. **Admin Interface**: Visit `http://localhost:8000/admin` to manage your data
3. **Health Check**: Visit `http://localhost:8000/health` to check server status

## Next Steps

Congratulations! You've created your first FABI+ application. Here's what you can explore next:

- [Installation & Setup](./installation-setup) - Detailed configuration options
- **API Development** - Advanced API features and patterns
- **Authentication** - Add user authentication and authorization
- **Admin Interface** - Customize the Django-style admin panel

:::tip Pro Tip
Use `fabiplus --help` to see all available CLI commands. The CLI is your best friend for rapid development!
:::
