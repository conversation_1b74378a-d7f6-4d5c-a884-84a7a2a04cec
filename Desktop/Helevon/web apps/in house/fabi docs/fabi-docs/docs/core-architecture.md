---
sidebar_position: 4
title: Core Architecture
description: Framework architecture, design patterns, and extension development guide for FABI+ applications.
keywords: [fabi+, architecture, framework, extensions, plugins, design patterns, core components]
---

import ORMTabs from '@site/src/components/ORMTabs';
import ORMCodeBlock from '@site/src/components/ORMCodeBlock';

# Core Architecture

This guide covers FABI+ framework internals, extension development, custom commands, and advanced architectural concepts.

## Framework Architecture

FABI+ follows a modular, layered architecture designed for scalability and maintainability.

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │    Views    │  │   Admin     │  │    CLI Commands     │ │
│  │             │  │ Interface   │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Service Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │ Authentication│ │   Caching   │  │  Background Tasks   │ │
│  │   & Permissions│ │   System    │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                      Core Layer                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │   Models    │  │  Database   │  │     Middleware      │ │
│  │   & ORM     │  │  Manager    │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │  FastAPI    │  │  Database   │  │    File System      │ │
│  │  Framework  │  │ (PostgreSQL,│  │   & Media Storage   │ │
│  │             │  │  SQLite)    │  │                     │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Design Principles

1. **Separation of Concerns**: Each layer has distinct responsibilities
2. **Dependency Injection**: Components are loosely coupled through DI
3. **Configuration-Driven**: Behavior controlled through settings
4. **Plugin Architecture**: Extensible through plugins and hooks
5. **Type Safety**: Comprehensive type hints throughout the codebase

## Core Components

### Application Factory

The application factory pattern creates and configures the FastAPI application:

<ORMCodeBlock
  language="python"
  code={`# fabiplus/core/application.py
from fastapi import FastAPI
from fabiplus.conf import settings
from fabiplus.core.database import init_database
from fabiplus.core.middleware import setup_middleware
from fabiplus.core.routing import setup_routes
from fabiplus.admin import setup_admin

class FABIPlusApplication:
    """Main application factory"""
    
    def __init__(self, settings_module: str = None):
        self.settings = settings
        self.app = None
        self._plugins = []
    
    def create_app(self) -> FastAPI:
        """Create and configure FastAPI application"""
        self.app = FastAPI(
            title=self.settings.PROJECT_NAME,
            version=self.settings.VERSION,
            description=self.settings.DESCRIPTION,
            docs_url="/docs" if self.settings.ENABLE_DOCS else None,
            redoc_url="/redoc" if self.settings.ENABLE_DOCS else None,
        )
        
        # Setup core components
        self._setup_database()
        self._setup_middleware()
        self._setup_routing()
        self._setup_admin()
        self._setup_plugins()
        
        return self.app
    
    def _setup_database(self):
        """Initialize database connection"""
        init_database(self.app, self.settings)
    
    def _setup_middleware(self):
        """Setup middleware stack"""
        setup_middleware(self.app, self.settings)
    
    def _setup_routing(self):
        """Setup application routes"""
        setup_routes(self.app, self.settings)
    
    def _setup_admin(self):
        """Setup admin interface"""
        if self.settings.ADMIN_ENABLED:
            setup_admin(self.app, self.settings)

# Usage
def create_app():
    factory = FABIPlusApplication()
    return factory.create_app()`}
/>

### Database Manager

The database manager handles ORM-agnostic database operations:

<ORMTabs
  sqlmodel={`# fabiplus/core/database.py
from typing import Any, Dict, Optional, Type
from contextlib import contextmanager
from sqlmodel import create_engine, Session, SQLModel

class SQLModelBackend:
    """SQLModel database backend"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
    
    def initialize(self, app: FastAPI, settings: Any):
        self.engine = create_engine(
            settings.DATABASE_URL,
            echo=settings.DATABASE_ECHO,
            **settings.SQLALCHEMY_ENGINE_OPTIONS
        )
        
        self.session_factory = lambda: Session(self.engine)
        
        # Add startup/shutdown events
        @app.on_event("startup")
        async def startup():
            self.create_tables()
        
        @app.on_event("shutdown")
        async def shutdown():
            self.engine.dispose()
    
    @contextmanager
    def get_session(self):
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def create_tables(self):
        SQLModel.metadata.create_all(self.engine)
    
    def drop_tables(self):
        SQLModel.metadata.drop_all(self.engine)`}
  sqlalchemy={`# fabiplus/core/database.py
from typing import Any, Dict, Optional, Type
from contextlib import contextmanager
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base

Base = declarative_base()

class SQLAlchemyBackend:
    """SQLAlchemy database backend"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
    
    def initialize(self, app: FastAPI, settings: Any):
        self.engine = create_engine(
            settings.DATABASE_URL,
            echo=settings.DATABASE_ECHO,
            **settings.SQLALCHEMY_ENGINE_OPTIONS
        )
        
        self.session_factory = sessionmaker(
            bind=self.engine,
            autocommit=False,
            autoflush=False
        )
        
        # Add startup/shutdown events
        @app.on_event("startup")
        async def startup():
            self.create_tables()
        
        @app.on_event("shutdown")
        async def shutdown():
            self.engine.dispose()
    
    @contextmanager
    def get_session(self):
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def create_tables(self):
        Base.metadata.create_all(self.engine)
    
    def drop_tables(self):
        Base.metadata.drop_all(self.engine)`}
  tortoise={`# fabiplus/core/database.py
from typing import Any, Dict, Optional, Type
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise

class TortoiseBackend:
    """Tortoise ORM database backend"""
    
    def __init__(self):
        self.config = None
    
    def initialize(self, app: FastAPI, settings: Any):
        self.config = {
            "connections": {
                "default": settings.DATABASE_URL
            },
            "apps": {
                "models": {
                    "models": settings.TORTOISE_MODELS,
                    "default_connection": "default",
                }
            }
        }
        
        register_tortoise(
            app,
            config=self.config,
            generate_schemas=True,
            add_exception_handlers=True,
        )
    
    async def get_session(self):
        # Tortoise doesn't use sessions like SQLAlchemy
        # Return connection context
        return Tortoise.get_connection("default")
    
    async def create_tables(self):
        await Tortoise.generate_schemas()
    
    async def drop_tables(self):
        await Tortoise._drop_databases()`}
  title="Database Backend Implementation"
/>

### Configuration System

The configuration system provides flexible, environment-aware settings:

<ORMCodeBlock
  language="python"
  code={`# fabiplus/conf/settings.py
from pydantic import BaseSettings, Field
from typing import List, Dict, Any, Optional
import os

class FABIPlusSettings(BaseSettings):
    """Base settings class for FABI+ applications"""
    
    # Project information
    PROJECT_NAME: str = Field(default="FABI+ API", env="PROJECT_NAME")
    VERSION: str = Field(default="1.0.0", env="VERSION")
    DESCRIPTION: str = Field(default="API built with FABI+", env="DESCRIPTION")
    
    # Environment
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=True, env="DEBUG")
    
    # Database
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_ECHO: bool = Field(default=False, env="DATABASE_ECHO")
    ORM_BACKEND: str = Field(default="sqlmodel", env="ORM_BACKEND")
    
    # Security
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ALLOWED_HOSTS: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # API Documentation
    ENABLE_DOCS: bool = Field(default=True, env="ENABLE_DOCS")
    DOCS_URL: str = Field(default="/docs", env="DOCS_URL")
    REDOC_URL: str = Field(default="/redoc", env="REDOC_URL")
    
    # Admin Interface
    ADMIN_ENABLED: bool = Field(default=True, env="ADMIN_ENABLED")
    ADMIN_PREFIX: str = Field(default="/admin", env="ADMIN_PREFIX")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    @property
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.ENVIRONMENT.lower() == "production"

# Settings registry for different environments
class DevelopmentSettings(FABIPlusSettings):
    """Development environment settings"""
    DEBUG: bool = True
    DATABASE_ECHO: bool = True

class ProductionSettings(FABIPlusSettings):
    """Production environment settings"""
    DEBUG: bool = False
    DATABASE_ECHO: bool = False
    ENABLE_DOCS: bool = False

def get_settings() -> FABIPlusSettings:
    """Get settings based on environment"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    settings_map = {
        "development": DevelopmentSettings,
        "production": ProductionSettings,
    }
    
    settings_class = settings_map.get(env, DevelopmentSettings)
    return settings_class()

# Global settings instance
settings = get_settings()`}
/>

## Extension Development

### Creating Extensions

Extensions allow you to add functionality to FABI+ projects. Here's how to create a blog analytics extension:

<ORMCodeBlock
  language="python"
  code={`# extensions/blog_analytics/extension.py
from fabiplus.core.extensions import Extension
from fabiplus.core.events import on
from fastapi import APIRouter

class BlogAnalyticsExtension(Extension):
    """Blog analytics extension"""
    
    name = "blog_analytics"
    version = "1.0.0"
    description = "Analytics for blog posts"
    
    def __init__(self, app, settings):
        super().__init__(app, settings)
        self.router = APIRouter(prefix="/analytics", tags=["analytics"])
    
    def initialize(self):
        """Initialize extension"""
        self._setup_routes()
        self._setup_event_listeners()
        self._setup_database()
        
        # Register router
        self.app.include_router(self.router)
    
    def _setup_routes(self):
        """Setup analytics routes"""
        
        @self.router.get("/posts/views")
        async def get_post_views():
            """Get post view statistics"""
            from .models import PostView
            
            views = await PostView.aggregate_views()
            return {"views": views}
    
    def _setup_event_listeners(self):
        """Setup event listeners"""
        
        @on("blog.post_viewed")
        async def track_post_view(event):
            """Track post view"""
            from .models import PostView
            
            post_id = event.data.get("post_id")
            user_id = event.data.get("user_id")
            ip_address = event.data.get("ip_address")
            
            await PostView.create(
                post_id=post_id,
                user_id=user_id,
                ip_address=ip_address
            )`}
/>

## Next Steps

Now that you understand FABI+'s core architecture:

1. **[Project Management](./project-management)** - Learn about project structure and organization
2. **[CLI Commands](./cli-commands)** - Master the comprehensive CLI toolkit
3. **[API Development](./api-development)** - Build robust APIs with advanced patterns
4. **[Advanced Features](./advanced-features)** - Explore caching, monitoring, and optimization

:::tip Extension Development
Check out our [Contributing Guide](./contributing) to learn how to contribute extensions back to the FABI+ ecosystem.
:::
