---
sidebar_position: 1
title: Introduction
description: Learn about Fabi+, a production-ready Python API framework that combines FastAPI's speed with Django's admin capabilities.
keywords: [fabi+, fastapi, django, python, api, framework, orm, sqlmodel, sqlalchemy]
---

import ORMSelector from '@site/src/components/ORMSelector';

# FABI+ Framework Introduction

## What is FABI+?

FABI+ (FastAPI + Django-style Admin + Batteries Included Plus) is a production-ready, API-only Python framework that combines the speed and modern features of FastAPI with the robustness and admin capabilities of Django. It's designed for developers who want to build high-performance APIs with minimal boilerplate while maintaining enterprise-grade features.

<ORMSelector />

## Key Features

### 🚀 **High Performance**
- Built on FastAPI for maximum speed and async support
- Memory-based caching system with Redis fallback
- Optimized database queries with multiple ORM support
- Production-ready with built-in performance monitoring

### 🛡️ **Security First**
- OAuth2 and JWT authentication out of the box
- Role-based access control (RBAC)
- Built-in rate limiting and CORS protection
- Security scanning and vulnerability detection

### 🎯 **Django-Style Admin**
- Full-featured admin interface with HTMX
- Model registration and automatic CRUD operations
- User management and permissions
- Custom admin views and dashboards

### 🔧 **Developer Experience**
- Django-style project and app scaffolding
- Comprehensive CLI with 40+ commands
- Hot reload development server
- Rich terminal output with progress indicators

### 📊 **Multiple ORM Support**
- **SQLModel** (default) - Modern, type-safe ORM
- **SQLAlchemy** - Mature, feature-rich ORM
- **Tortoise ORM** - Async-native ORM (coming soon)

### 🗄️ **Database Features**
- Automatic migrations with Alembic
- Multiple database backends (SQLite, PostgreSQL, MySQL)
- Connection pooling and optimization
- Database introspection and management

### 📁 **Media System**
- File upload and validation
- Image processing with thumbnails
- PDF processing and metadata extraction
- Configurable storage backends

## Framework Philosophy

FABI+ follows these core principles:

1. **Convention over Configuration** - Sensible defaults with easy customization
2. **DRY (Don't Repeat Yourself)** - Reusable components and patterns
3. **Scalability First** - Built for growth from day one
4. **Developer Happiness** - Intuitive APIs and excellent tooling
5. **Production Ready** - Enterprise features included by default

## Comparison with Other Frameworks

### vs. Django
| Feature | FABI+ | Django |
|---------|-------|--------|
| **Performance** | ⚡ FastAPI-based, async-first | 🐌 Synchronous, slower |
| **API Development** | 🎯 API-only, OpenAPI docs | 🔧 Requires DRF setup |
| **Admin Interface** | ✅ Modern HTMX-based | ✅ Mature but dated |
| **Type Safety** | ✅ Full Pydantic integration | ❌ Limited typing |
| **Learning Curve** | 📈 Moderate | 📈 Steep |

### vs. FastAPI
| Feature | FABI+ | FastAPI |
|---------|-------|---------|
| **Admin Interface** | ✅ Built-in Django-style | ❌ None |
| **Project Structure** | ✅ Django-style scaffolding | ❌ Manual setup |
| **User Management** | ✅ Built-in with CLI | ❌ Manual implementation |
| **Database Migrations** | ✅ Automatic with Alembic | ❌ Manual setup |
| **Caching** | ✅ Built-in multi-backend | ❌ Manual implementation |

### vs. Flask
| Feature | FABI+ | Flask |
|---------|-------|-------|
| **Performance** | ⚡ Async, high-performance | 🐌 Synchronous |
| **Built-in Features** | 🎁 Batteries included | 🔧 Minimal, requires extensions |
| **API Documentation** | ✅ Automatic OpenAPI | ❌ Manual or extensions |
| **Type Safety** | ✅ Full Pydantic support | ❌ Limited |
| **Admin Interface** | ✅ Built-in | ❌ Requires Flask-Admin |

## System Requirements

### Minimum Requirements
- **Python**: 3.10 or higher
- **Memory**: 512MB RAM
- **Storage**: 100MB free space
- **OS**: Linux, macOS, or Windows

### Recommended Requirements
- **Python**: 3.11 or higher
- **Memory**: 2GB RAM or more
- **Storage**: 1GB free space
- **Database**: PostgreSQL 12+ (for production)

## Next Steps

Ready to get started? Check out our [Quick Start Guide](./quick-start) to build your first Fabi+ application in minutes, or dive into the [Installation & Setup](./installation-setup) for detailed configuration options.

:::tip Choose Your ORM
Use the ORM selector above to customize all code examples throughout this documentation to match your preferred ORM. Your selection will persist across all pages.
:::
