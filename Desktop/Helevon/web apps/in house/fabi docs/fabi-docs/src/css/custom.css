/**
 * Fabi+ Documentation Custom Styles
 * Modern, matte finish design with tech-forward aesthetic
 */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Light theme variables */
:root {
  /* Fabi+ Brand Colors */
  --ifm-color-primary: #6366f1;
  --ifm-color-primary-dark: #4f46e5;
  --ifm-color-primary-darker: #4338ca;
  --ifm-color-primary-darkest: #3730a3;
  --ifm-color-primary-light: #818cf8;
  --ifm-color-primary-lighter: #a5b4fc;
  --ifm-color-primary-lightest: #c7d2fe;

  /* Typography */
  --ifm-font-family-base: 'Inter', system-ui, -apple-system, sans-serif;
  --ifm-font-family-monospace: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --ifm-code-font-size: 0.875rem;
  --ifm-font-size-base: 16px;
  --ifm-line-height-base: 1.6;

  /* Spacing and Layout */
  --ifm-spacing-horizontal: 1.5rem;
  --ifm-navbar-height: 4rem;
  --ifm-sidebar-width: 280px;

  /* Colors */
  --ifm-background-color: #ffffff;
  --ifm-background-surface-color: #f8fafc;
  --ifm-color-content: #334155;
  --ifm-color-content-secondary: #64748b;
  --ifm-heading-color: #0f172a;

  /* Code highlighting */
  --docusaurus-highlighted-code-line-bg: rgba(99, 102, 241, 0.1);

  /* Borders and shadows */
  --ifm-global-border-width: 1px;
  --ifm-global-radius: 8px;
  --ifm-hover-overlay: rgba(99, 102, 241, 0.05);
}

/* Matte Dark Theme */
[data-theme='dark'] {
  /* Fabi+ Dark Brand Colors */
  --ifm-color-primary: #818cf8;
  --ifm-color-primary-dark: #6366f1;
  --ifm-color-primary-darker: #4f46e5;
  --ifm-color-primary-darkest: #4338ca;
  --ifm-color-primary-light: #a5b4fc;
  --ifm-color-primary-lighter: #c7d2fe;
  --ifm-color-primary-lightest: #e0e7ff;

  /* Matte Dark Backgrounds - No glossy effects */
  --ifm-background-color: #0f172a;
  --ifm-background-surface-color: #1e293b;
  --ifm-color-emphasis-0: #334155;
  --ifm-color-emphasis-100: #475569;
  --ifm-color-emphasis-200: #64748b;
  --ifm-color-emphasis-300: #94a3b8;

  /* Text Colors */
  --ifm-color-content: #e2e8f0;
  --ifm-color-content-secondary: #cbd5e1;
  --ifm-heading-color: #f1f5f9;

  /* Code highlighting */
  --docusaurus-highlighted-code-line-bg: rgba(129, 140, 248, 0.15);

  /* Matte finish - remove glossy effects */
  --ifm-hover-overlay: rgba(129, 140, 248, 0.1);

  /* Navbar and sidebar matte styling */
  --ifm-navbar-background-color: #1e293b;
  --ifm-navbar-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);

  /* Remove any gradients or glossy effects */
  --ifm-menu-color-background-active: rgba(129, 140, 248, 0.1);
  --ifm-menu-color-background-hover: rgba(129, 140, 248, 0.05);
}

/* Modern Component Styling */
.search-shortcut {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  color: var(--ifm-color-content-secondary);
  background: var(--ifm-color-emphasis-0);
  border-radius: 4px;
  border: 1px solid var(--ifm-color-emphasis-200);
  margin-left: 0.5rem;
}

.search-shortcut kbd {
  background: none;
  border: none;
  padding: 0;
  font-size: inherit;
  color: inherit;
}

/* Enhanced Code Blocks */
.prism-code {
  border-radius: var(--ifm-global-radius);
  font-family: var(--ifm-font-family-monospace);
  font-size: var(--ifm-code-font-size);
  line-height: 1.5;
}

/* Smooth transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Remove any remaining glossy effects */
.navbar__item,
.menu__link,
.button {
  background-image: none !important;
  box-shadow: none !important;
}

/* Modern button styling */
.button--primary {
  background: var(--ifm-color-primary);
  border: none;
  font-weight: 500;
  letter-spacing: 0.025em;
}

.button--primary:hover {
  background: var(--ifm-color-primary-dark);
  transform: translateY(-1px);
}

/* Enhanced sidebar */
.theme-doc-sidebar-container {
  border-right: 1px solid var(--ifm-color-emphasis-200);
}

[data-theme='dark'] .theme-doc-sidebar-container {
  border-right-color: var(--ifm-color-emphasis-0);
}

/* Modern table styling */
table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--ifm-global-radius);
  overflow: hidden;
  border: 1px solid var(--ifm-color-emphasis-200);
}

/* Responsive design improvements */
@media (max-width: 996px) {
  .search-shortcut {
    display: none;
  }
}
