import React from 'react';
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import { useORM } from '../../contexts/ORMContext';
import ORMCodeBlock from '../ORMCodeBlock';

interface ORMTabsProps {
  sqlmodel?: string;
  sqlalchemy?: string;
  tortoise?: string;
  language?: string;
  title?: string;
  showLineNumbers?: boolean;
  className?: string;
}

const ORMTabs: React.FC<ORMTabsProps> = ({
  sqlmodel,
  sqlalchemy,
  tortoise,
  language = 'python',
  title,
  showLineNumbers = false,
  className = '',
}) => {
  const { selectedORM, setSelectedORM, ormConfig } = useORM();

  const availableTabs = [
    { key: 'sqlmodel', code: sqlmodel },
    { key: 'sqlalchemy', code: sqlalchemy },
    { key: 'tortoise', code: tortoise },
  ].filter(tab => tab.code);

  if (availableTabs.length === 0) {
    return (
      <div className="alert alert--warning">
        <p>No code examples available.</p>
      </div>
    );
  }

  if (availableTabs.length === 1) {
    // If only one tab is available, just show the code block
    const tab = availableTabs[0];
    return (
      <ORMCodeBlock
        {...{ [tab.key]: tab.code }}
        language={language}
        title={title}
        showLineNumbers={showLineNumbers}
        className={className}
      />
    );
  }

  return (
    <Tabs
      defaultValue={selectedORM}
      values={availableTabs.map(tab => ({
        label: ormConfig[tab.key as keyof typeof ormConfig].displayName,
        value: tab.key,
      }))}
      className={className}
      onTabChange={(value) => setSelectedORM(value as any)}
    >
      {availableTabs.map(tab => (
        <TabItem key={tab.key} value={tab.key}>
          <ORMCodeBlock
            {...{ [tab.key]: tab.code }}
            language={language}
            title={title}
            showLineNumbers={showLineNumbers}
          />
        </TabItem>
      ))}
    </Tabs>
  );
};

export default ORMTabs;
