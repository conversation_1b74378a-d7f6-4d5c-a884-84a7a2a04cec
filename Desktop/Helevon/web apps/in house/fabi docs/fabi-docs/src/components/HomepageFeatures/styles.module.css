.features {
  display: flex;
  align-items: center;
  padding: 4rem 0;
  width: 100%;
  background: var(--ifm-background-surface-color);
}

.featuresHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.featuresTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--ifm-heading-color);
  margin-bottom: 1rem;
}

.featuresSubtitle {
  font-size: 1.25rem;
  color: var(--ifm-color-content-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.featureCard {
  height: 100%;
  padding: 2rem;
  background: var(--ifm-background-color);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: var(--ifm-global-radius);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.featureCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--ifm-color-primary);
}

[data-theme='dark'] .featureCard:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.featureIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--ifm-color-primary), var(--ifm-color-primary-light));
  border-radius: 50%;
  margin: 0 auto 1.5rem;
}

.icon {
  font-size: 2rem;
  filter: grayscale(1) brightness(0) invert(1);
}

.featureContent {
  text-align: center;
}

.featureTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--ifm-heading-color);
  margin-bottom: 1rem;
}

.featureDescription {
  color: var(--ifm-color-content-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Responsive design */
@media (max-width: 996px) {
  .featuresTitle {
    font-size: 2rem;
  }

  .featuresSubtitle {
    font-size: 1.125rem;
  }

  .featureCard {
    padding: 1.5rem;
  }

  .featureIcon {
    width: 60px;
    height: 60px;
  }

  .icon {
    font-size: 1.5rem;
  }
}
