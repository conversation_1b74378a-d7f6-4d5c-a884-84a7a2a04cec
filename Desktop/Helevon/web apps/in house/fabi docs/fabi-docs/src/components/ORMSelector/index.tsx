import React from 'react';
import { useORM, ORMType } from '../../contexts/ORMContext';
import styles from './styles.module.css';

interface ORMSelectorProps {
  className?: string;
  size?: 'small' | 'medium' | 'large';
}

const ORMSelector: React.FC<ORMSelectorProps> = ({ className = '', size = 'medium' }) => {
  const { selectedORM, setSelectedORM, ormConfig } = useORM();

  const handleORMChange = (orm: ORMType) => {
    if (ormConfig[orm].status !== 'coming-soon') {
      setSelectedORM(orm);
    }
  };

  return (
    <div className={`${styles.ormSelector} ${styles[size]} ${className}`}>
      <div className={styles.selectorHeader}>
        <span className={styles.label}>Choose your ORM:</span>
      </div>
      <div className={styles.options}>
        {Object.entries(ormConfig).map(([key, config]) => {
          const isSelected = selectedORM === key;
          const isDisabled = config.status === 'coming-soon';
          
          return (
            <button
              key={key}
              className={`${styles.option} ${isSelected ? styles.selected : ''} ${
                isDisabled ? styles.disabled : ''
              }`}
              onClick={() => handleORMChange(key as ORMType)}
              disabled={isDisabled}
              style={{
                '--orm-color': config.color,
              } as React.CSSProperties}
            >
              <div className={styles.optionContent}>
                <div className={styles.optionHeader}>
                  <span className={styles.optionName}>{config.displayName}</span>
                  {config.status === 'recommended' && (
                    <span className={styles.badge}>Recommended</span>
                  )}
                  {config.status === 'coming-soon' && (
                    <span className={styles.badgeComingSoon}>Coming Soon</span>
                  )}
                </div>
                <p className={styles.optionDescription}>{config.description}</p>
              </div>
              {isSelected && <div className={styles.selectedIndicator} />}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default ORMSelector;
