.ormSelector {
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--ifm-background-surface-color);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: var(--ifm-global-radius);
}

.selectorHeader {
  margin-bottom: 1rem;
}

.label {
  font-weight: 600;
  color: var(--ifm-heading-color);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 0.75rem;
}

.option {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: var(--ifm-background-color);
  border: 2px solid var(--ifm-color-emphasis-200);
  border-radius: var(--ifm-global-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  font-family: inherit;
  width: 100%;
}

.option:hover:not(.disabled) {
  border-color: var(--orm-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme='dark'] .option:hover:not(.disabled) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.option.selected {
  border-color: var(--orm-color);
  background: rgba(var(--orm-color), 0.05);
}

.option.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.optionContent {
  flex: 1;
}

.optionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.optionName {
  font-weight: 600;
  font-size: 1rem;
  color: var(--ifm-heading-color);
}

.badge {
  padding: 0.125rem 0.5rem;
  background: var(--ifm-color-primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badgeComingSoon {
  padding: 0.125rem 0.5rem;
  background: var(--ifm-color-emphasis-300);
  color: var(--ifm-color-content);
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.optionDescription {
  margin: 0;
  font-size: 0.875rem;
  color: var(--ifm-color-content-secondary);
  line-height: 1.4;
}

.selectedIndicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 8px;
  height: 8px;
  background: var(--orm-color);
  border-radius: 50%;
}

/* Size variants */
.small {
  padding: 0.75rem;
}

.small .options {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
}

.small .option {
  padding: 0.75rem;
}

.small .optionName {
  font-size: 0.875rem;
}

.small .optionDescription {
  font-size: 0.75rem;
}

.large {
  padding: 1.5rem;
}

.large .options {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1rem;
}

.large .option {
  padding: 1.5rem;
}

.large .optionName {
  font-size: 1.125rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .options {
    grid-template-columns: 1fr;
  }
  
  .optionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
