import type {ReactNode} from 'react';
import clsx from 'clsx';
import Heading from '@theme/Heading';
import styles from './styles.module.css';

type FeatureItem = {
  title: string;
  icon: string;
  description: ReactNode;
};

const FeatureList: FeatureItem[] = [
  {
    title: 'FastAPI Performance',
    icon: '⚡',
    description: (
      <>
        Built on FastAPI for maximum speed and async support. Get the performance
        of modern Python with the convenience of Django-style development.
      </>
    ),
  },
  {
    title: 'Django-Style Admin',
    icon: '🎛️',
    description: (
      <>
        Full-featured admin interface with HTMX. Manage your data with the
        familiar Django admin experience, modernized for today's web.
      </>
    ),
  },
  {
    title: 'Multiple ORM Support',
    icon: '🗄️',
    description: (
      <>
        Choose from SQLModel, SQLAlchemy, or Tortoise ORM. Use the ORM that
        fits your project needs with consistent APIs across all options.
      </>
    ),
  },
  {
    title: 'Developer Experience',
    icon: '🔧',
    description: (
      <>
        Comprehensive CLI with 40+ commands, hot reload development server,
        and rich terminal output. Built for developer happiness.
      </>
    ),
  },
  {
    title: 'Security First',
    icon: '🛡️',
    description: (
      <>
        OAuth2 and JWT authentication, role-based access control, rate limiting,
        and CORS protection built-in. Security is not an afterthought.
      </>
    ),
  },
  {
    title: 'Production Ready',
    icon: '🚀',
    description: (
      <>
        Memory-based caching, database optimization, monitoring, and deployment
        tools. Scale from prototype to production seamlessly.
      </>
    ),
  },
];

function Feature({title, icon, description}: FeatureItem) {
  return (
    <div className={clsx('col col--4')}>
      <div className={styles.featureCard}>
        <div className={styles.featureIcon}>
          <span className={styles.icon}>{icon}</span>
        </div>
        <div className={styles.featureContent}>
          <Heading as="h3" className={styles.featureTitle}>{title}</Heading>
          <p className={styles.featureDescription}>{description}</p>
        </div>
      </div>
    </div>
  );
}

export default function HomepageFeatures(): ReactNode {
  return (
    <section className={styles.features}>
      <div className="container">
        <div className={styles.featuresHeader}>
          <Heading as="h2" className={styles.featuresTitle}>Why Choose Fabi+?</Heading>
          <p className={styles.featuresSubtitle}>
            The perfect blend of FastAPI's performance and Django's developer experience
          </p>
        </div>
        <div className="row">
          {FeatureList.map((props, idx) => (
            <Feature key={idx} {...props} />
          ))}
        </div>
      </div>
    </section>
  );
}
