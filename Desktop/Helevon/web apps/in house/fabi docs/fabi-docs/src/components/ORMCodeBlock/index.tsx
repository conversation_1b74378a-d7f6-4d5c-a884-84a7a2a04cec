import React from 'react';
import CodeBlock from '@theme/CodeBlock';
import { useORM, ORMType } from '../../contexts/ORMContext';

interface ORMCodeBlockProps {
  sqlmodel?: string;
  sqlalchemy?: string;
  tortoise?: string;
  language?: string;
  title?: string;
  showLineNumbers?: boolean;
  className?: string;
}

interface ORMCodeExamples {
  [key: string]: {
    [orm in ORMType]?: string;
  };
}

// Common code examples for different ORMs
const commonExamples: ORMCodeExamples = {
  userModel: {
    sqlmodel: `from sqlmodel import SQLModel, Field
from typing import Optional
import uuid
from datetime import datetime

class User(SQLModel, table=True):
    __tablename__ = "users"
    
    id: uuid.UUID = Field(primary_key=True, default_factory=uuid.uuid4)
    username: str = Field(unique=True, max_length=150)
    email: str = Field(unique=True, max_length=254)
    first_name: Optional[str] = Field(max_length=150)
    last_name: Optional[str] = Field(max_length=150)
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)`,
    
    sqlalchemy: `from sqlalchemy import Column, String, Boolean, DateTime, UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import uuid

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(150), unique=True, nullable=False)
    email = Column(String(254), unique=True, nullable=False)
    first_name = Column(String(150), nullable=True)
    last_name = Column(String(150), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, server_default=func.now())`,
    
    tortoise: `from tortoise.models import Model
from tortoise import fields
from typing import Optional

class User(Model):
    id = fields.UUIDField(pk=True)
    username = fields.CharField(max_length=150, unique=True)
    email = fields.CharField(max_length=254, unique=True)
    first_name = fields.CharField(max_length=150, null=True)
    last_name = fields.CharField(max_length=150, null=True)
    is_active = fields.BooleanField(default=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    
    class Meta:
        table = "users"`
  },
  
  databaseQuery: {
    sqlmodel: `from sqlmodel import Session, select

# Create a new user
def create_user(session: Session, username: str, email: str) -> User:
    user = User(username=username, email=email)
    session.add(user)
    session.commit()
    session.refresh(user)
    return user

# Query users
def get_users(session: Session) -> list[User]:
    statement = select(User).where(User.is_active == True)
    return session.exec(statement).all()`,
    
    sqlalchemy: `from sqlalchemy.orm import Session

# Create a new user
def create_user(session: Session, username: str, email: str) -> User:
    user = User(username=username, email=email)
    session.add(user)
    session.commit()
    session.refresh(user)
    return user

# Query users
def get_users(session: Session) -> list[User]:
    return session.query(User).filter(User.is_active == True).all()`,
    
    tortoise: `# Create a new user
async def create_user(username: str, email: str) -> User:
    user = await User.create(username=username, email=email)
    return user

# Query users
async def get_users() -> list[User]:
    return await User.filter(is_active=True).all()`
  }
};

const ORMCodeBlock: React.FC<ORMCodeBlockProps> = ({
  sqlmodel,
  sqlalchemy,
  tortoise,
  language = 'python',
  title,
  showLineNumbers = false,
  className = '',
}) => {
  const { selectedORM } = useORM();

  // Determine which code to show
  let codeToShow = '';
  
  if (sqlmodel && selectedORM === 'sqlmodel') {
    codeToShow = sqlmodel;
  } else if (sqlalchemy && selectedORM === 'sqlalchemy') {
    codeToShow = sqlalchemy;
  } else if (tortoise && selectedORM === 'tortoise') {
    codeToShow = tortoise;
  } else {
    // Fallback to the selected ORM or first available
    codeToShow = sqlmodel || sqlalchemy || tortoise || '';
  }

  if (!codeToShow) {
    return (
      <div className="alert alert--warning">
        <p>Code example not available for {selectedORM.toUpperCase()}.</p>
      </div>
    );
  }

  return (
    <CodeBlock
      language={language}
      title={title}
      showLineNumbers={showLineNumbers}
      className={className}
    >
      {codeToShow}
    </CodeBlock>
  );
};

// Export common examples for easy use
export { commonExamples };
export default ORMCodeBlock;
