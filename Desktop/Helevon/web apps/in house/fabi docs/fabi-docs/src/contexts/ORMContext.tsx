import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type ORMType = 'sqlmodel' | 'sqlalchemy' | 'tortoise';

export interface ORMContextType {
  selectedORM: ORMType;
  setSelectedORM: (orm: ORMType) => void;
  ormConfig: {
    [key in ORMType]: {
      name: string;
      displayName: string;
      status: 'recommended' | 'stable' | 'coming-soon';
      description: string;
      color: string;
    };
  };
}

const ormConfig = {
  sqlmodel: {
    name: 'sqlmodel',
    displayName: 'SQLModel',
    status: 'recommended' as const,
    description: 'Modern, type-safe ORM built on SQLAlchemy',
    color: '#6366f1',
  },
  sqlalchemy: {
    name: 'sqlalchemy',
    displayName: 'SQLAlchemy',
    status: 'stable' as const,
    description: 'Mature, feature-rich Python SQL toolkit',
    color: '#059669',
  },
  tortoise: {
    name: 'tortoise',
    displayName: 'Tortoise ORM',
    status: 'coming-soon' as const,
    description: 'Async-native ORM inspired by Django ORM',
    color: '#dc2626',
  },
};

const ORMContext = createContext<ORMContextType | undefined>(undefined);

interface ORMProviderProps {
  children: ReactNode;
}

export const ORMProvider: React.FC<ORMProviderProps> = ({ children }) => {
  const [selectedORM, setSelectedORMState] = useState<ORMType>('sqlmodel');

  // Load saved ORM preference from localStorage
  useEffect(() => {
    const savedORM = localStorage.getItem('fabi-docs-selected-orm') as ORMType;
    if (savedORM && ormConfig[savedORM]) {
      setSelectedORMState(savedORM);
    }
  }, []);

  // Save ORM preference to localStorage
  const setSelectedORM = (orm: ORMType) => {
    setSelectedORMState(orm);
    localStorage.setItem('fabi-docs-selected-orm', orm);
  };

  const value: ORMContextType = {
    selectedORM,
    setSelectedORM,
    ormConfig,
  };

  return <ORMContext.Provider value={value}>{children}</ORMContext.Provider>;
};

export const useORM = (): ORMContextType => {
  const context = useContext(ORMContext);
  if (context === undefined) {
    throw new Error('useORM must be used within an ORMProvider');
  }
  return context;
};
