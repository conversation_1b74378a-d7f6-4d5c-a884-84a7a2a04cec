/**
 * Fabi+ Homepage Styles
 * Modern, matte finish design with tech-forward aesthetic
 */

.heroBanner {
  padding: 6rem 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--ifm-color-primary) 0%, var(--ifm-color-primary-dark) 100%);
  color: white;
}

[data-theme='dark'] .heroBanner {
  background: linear-gradient(135deg, var(--ifm-background-color) 0%, var(--ifm-background-surface-color) 100%);
  border-bottom: 1px solid var(--ifm-color-emphasis-200);
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[data-theme='dark'] .heroTitle {
  background: linear-gradient(45deg, var(--ifm-color-primary-light), var(--ifm-color-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.heroSubtitle {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.heroDescription {
  max-width: 600px;
  margin: 0 auto 2.5rem;
  font-size: 1.125rem;
  line-height: 1.6;
  opacity: 0.85;
}

.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.heroOrm {
  max-width: 800px;
  margin: 0 auto;
}

/* Responsive design */
@media screen and (max-width: 996px) {
  .heroBanner {
    padding: 4rem 2rem;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.25rem;
  }

  .heroDescription {
    font-size: 1rem;
  }

  .buttons {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media screen and (max-width: 768px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 1.125rem;
  }
}
