import type {ReactNode} from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import useDocusaurusContext from '@docusaurus/useDocusaurusContext';
import Layout from '@theme/Layout';
import HomepageFeatures from '@site/src/components/HomepageFeatures';
import ORMSelector from '@site/src/components/ORMSelector';
import Heading from '@theme/Heading';

import styles from './index.module.css';

function HomepageHeader() {
  const {siteConfig} = useDocusaurusContext();
  return (
    <header className={clsx('hero hero--primary', styles.heroBanner)}>
      <div className="container">
        <Heading as="h1" className={styles.heroTitle}>
          {siteConfig.title}
        </Heading>
        <p className={styles.heroSubtitle}>{siteConfig.tagline}</p>
        <div className={styles.heroDescription}>
          <p>
            The production-ready Python API framework that combines FastAPI's speed
            with Django's admin capabilities. Build high-performance APIs with minimal boilerplate.
          </p>
        </div>
        <div className={styles.buttons}>
          <Link
            className="button button--primary button--lg"
            to="/docs/introduction">
            Get Started 🚀
          </Link>
          <Link
            className="button button--secondary button--lg"
            to="/docs/quick-start">
            Quick Start ⏱️
          </Link>
        </div>
        <div className={styles.heroOrm}>
          <ORMSelector size="small" />
        </div>
      </div>
    </header>
  );
}

export default function Home(): ReactNode {
  const {siteConfig} = useDocusaurusContext();
  return (
    <Layout
      title={`${siteConfig.title} - Fast, Modern, Production-Ready Python API Framework`}
      description="Fabi+ combines FastAPI's performance with Django's admin capabilities. Build production-ready APIs with SQLModel, SQLAlchemy, or Tortoise ORM support.">
      <HomepageHeader />
      <main>
        <HomepageFeatures />
      </main>
    </Layout>
  );
}
